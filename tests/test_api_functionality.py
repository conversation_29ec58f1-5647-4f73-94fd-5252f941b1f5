#!/usr/bin/env python3
#!/usr/bin/env python3
"""
Day8 API功能测试
测试Day8系统的API调用功能
"""

import sys
import os
import json
import time
import traceback
from datetime import datetime
from test_utils import TestUtils

# 添加项目路径
TestUtils.add_project_to_path()

class Day8APIFunctionTest:
    """Day8 API功能测试类"""
    
    def __init__(self):
        self.test_results = []
        self.session = None
        
    def test_api_authentication(self):
        """测试API认证"""
        print("🧪 测试1: API认证测试")
        
        try:
            import day8
            self.session = day8.sign_in()
            
            if self.session:
                # 简单测试会话是否有效
                test_response = self.session.get('https://api.worldquantbrain.com/data-fields?limit=1')
                if test_response.status_code == 200:
                    self.record_result("API认证", True, "认证成功，会话有效")
                else:
                    self.record_result("API认证", False, f"认证后会话无效，状态码: {test_response.status_code}")
            else:
                self.record_result("API认证", False, "认证失败，未获得会话")
                
        except Exception as e:
            self.record_result("API认证", False, f"认证过程异常: {str(e)}")
    
    def test_datafields_retrieval(self):
        """测试数据字段获取"""
        print("🧪 测试2: 数据字段获取测试")
        
        if not self.session:
            self.record_result("数据字段获取", False, "会话未建立")
            return
        
        try:
            import day8
            
            # 使用分析师数据集进行测试
            search_scope = {
                'instrumentType': 'EQUITY',
                'region': 'USA',
                'delay': '0',
                'universe': 'TOP3000'
            }
            
            datafields_df = day8.get_datafields(self.session, search_scope, 'analyst4')
            
            if not datafields_df.empty:
                matrix_fields = datafields_df[datafields_df['type'] == "MATRIX"]
                if not matrix_fields.empty:
                    self.record_result("数据字段获取", True, f"成功获取{len(matrix_fields)}个MATRIX字段")
                else:
                    self.record_result("数据字段获取", False, "未找到MATRIX类型字段")
            else:
                self.record_result("数据字段获取", False, "未获取到任何数据字段")
                
        except Exception as e:
            self.record_result("数据字段获取", False, f"数据字段获取异常: {str(e)}")
    
    def test_alpha_submission(self):
        """测试Alpha提交（轻量级测试）"""
        print("🧪 测试3: Alpha提交测试")
        
        if not self.session:
            self.record_result("Alpha提交", False, "会话未建立")
            return
        
        try:
            # 创建一个简单的测试Alpha
            test_alpha = {
                "type": "REGULAR",
                "settings": {
                    "instrumentType": "EQUITY",
                    "region": "USA",
                    "universe": "TOP3000",
                    "delay": 0,
                    "decay": 0,
                    "neutralization": "SUBINDUSTRY",
                    "truncation": 0.01,
                    "pasteurization": "ON",
                    "unitHandling": "VERIFY",
                    "nanHandling": "OFF",
                    "language": "FASTEXPR",
                    "visualization": False,
                },
                "regular": "rank(close)"  # 简单的测试表达式
            }
            
            # 提交Alpha
            response = self.session.post(
                'https://api.worldquantbrain.com/simulations',
                json=test_alpha,
                timeout=30
            )
            
            if response.status_code == 201:
                location = response.headers.get('Location', '')
                self.record_result("Alpha提交", True, f"Alpha提交成功，Location: {location[:50]}...")
            else:
                self.record_result("Alpha提交", False, f"Alpha提交失败，状态码: {response.status_code}")
                
        except Exception as e:
            self.record_result("Alpha提交", False, f"Alpha提交异常: {str(e)}")
    
    def test_session_health_check(self):
        """测试会话健康检查"""
        print("🧪 测试4: 会话健康检查测试")
        
        if not self.session:
            self.record_result("会话健康检查", False, "会话未建立")
            return
        
        try:
            import day8
            
            # 测试会话有效性检查
            is_valid = day8.check_session_validity(self.session)
            
            if is_valid:
                self.record_result("会话健康检查", True, "会话健康检查通过")
            else:
                self.record_result("会话健康检查", False, "会话健康检查失败")
                
        except Exception as e:
            self.record_result("会话健康检查", False, f"会话健康检查异常: {str(e)}")
    
    def test_configuration_loading(self):
        """测试配置加载"""
        print("🧪 测试5: 配置加载测试")
        
        with TestUtils.project_working_directory():
            try:
                import day8
                
                # 测试数据集配置加载
                datasets_config = day8.load_datasets_config()
                operators_config = day8.load_operators_config()
                combinations_config = day8.load_combinations_config()
                
                success_count = 0
                total_count = 3
                
                if datasets_config:
                    success_count += 1
                    print("   ✅ 数据集配置加载成功")
                else:
                    print("   ❌ 数据集配置加载失败")
                
                if operators_config:
                    success_count += 1
                    print("   ✅ 运算符配置加载成功")
                else:
                    print("   ❌ 运算符配置加载失败")
                
                if combinations_config:
                    success_count += 1
                    print("   ✅ 组合配置加载成功")
                else:
                    print("   ❌ 组合配置加载失败")
                
                if success_count == total_count:
                    self.record_result("配置加载", True, f"所有{total_count}个配置文件加载成功")
                else:
                    self.record_result("配置加载", False, f"只有{success_count}/{total_count}个配置文件加载成功")
                    
            except Exception as e:
                self.record_result("配置加载", False, f"配置加载异常: {str(e)}")
    
    def test_manager_initialization(self):
        """测试管理器初始化"""
        print("🧪 测试6: 管理器初始化测试")
        
        with TestUtils.project_working_directory():
            try:
                import day8
                
                managers = {}
                success_count = 0
                
                # 测试DatasetManager
                try:
                    managers['DatasetManager'] = day8.DatasetManager()
                    success_count += 1
                    print("   ✅ DatasetManager 初始化成功")
                except Exception as e:
                    print(f"   ❌ DatasetManager 初始化失败: {str(e)}")
                
                # 测试OperatorLibrary
                try:
                    managers['OperatorLibrary'] = day8.OperatorLibrary()
                    success_count += 1
                    print("   ✅ OperatorLibrary 初始化成功")
                except Exception as e:
                    print(f"   ❌ OperatorLibrary 初始化失败: {str(e)}")
                
                # 测试CombinationManager
                try:
                    managers['CombinationManager'] = day8.CombinationManager()
                    success_count += 1
                    print("   ✅ CombinationManager 初始化成功")
                except Exception as e:
                    print(f"   ❌ CombinationManager 初始化失败: {str(e)}")
                
                # 测试CheckpointManager
                try:
                    managers['CheckpointManager'] = day8.CheckpointManager()
                    success_count += 1
                    print("   ✅ CheckpointManager 初始化成功")
                except Exception as e:
                    print(f"   ❌ CheckpointManager 初始化失败: {str(e)}")
                
                # 测试ProgressTracker
                try:
                    managers['ProgressTracker'] = day8.ProgressTracker()
                    success_count += 1
                    print("   ✅ ProgressTracker 初始化成功")
                except Exception as e:
                    print(f"   ❌ ProgressTracker 初始化失败: {str(e)}")
                
                total_managers = 5
                if success_count == total_managers:
                    self.record_result("管理器初始化", True, f"所有{total_managers}个管理器初始化成功")
                else:
                    self.record_result("管理器初始化", False, f"只有{success_count}/{total_managers}个管理器初始化成功")
                    
            except Exception as e:
                self.record_result("管理器初始化", False, f"管理器初始化异常: {str(e)}")
    
    def record_result(self, test_name, success, message):
        """记录测试结果"""
        self.test_results.append({
            'test_name': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat()
        })
    
    def run_all_tests(self):
        """运行所有API测试"""
        print("🚀 开始Day8 API功能测试")
        print("=" * 60)
        
        # API认证测试
        self.test_api_authentication()
        
        # 配置加载测试
        self.test_configuration_loading()
        
        # 管理器初始化测试
        self.test_manager_initialization()
        
        # 会话健康检查测试
        self.test_session_health_check()
        
        # API功能测试（需要网络连接）
        with TestUtils.project_working_directory():
            print("\n⚠️  API测试需要网络连接的测试...")
            time.sleep(2)
            self.test_datafields_retrieval()
            
            # Alpha提交测试（轻量级）
            self.test_alpha_submission()
        
        # 输出测试结果
        self.print_summary()
        
        # 保存测试结果
        self.save_results()
    
    def print_summary(self):
        """打印测试摘要"""
        print("\n" + "=" * 60)
        print("📊 API功能测试结果摘要")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests} ✅")
        print(f"失败测试: {failed_tests} ❌")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        print("\n📋 详细结果:")
        for result in self.test_results:
            status = "✅" if result['success'] else "❌"
            print(f"{status} {result['test_name']}: {result['message']}")
    
    def save_results(self):
        """保存测试结果"""
        try:
            test_dir = os.path.dirname(__file__)
            results_file = os.path.join(test_dir, 'api_function_test_results.json')
            
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'test_info': {
                        'test_type': 'api_functionality',
                        'test_time': datetime.now().isoformat(),
                        'total_tests': len(self.test_results),
                        'passed_tests': sum(1 for result in self.test_results if result['success']),
                        'failed_tests': sum(1 for result in self.test_results if not result['success'])
                    },
                    'test_results': self.test_results
                }, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 测试结果已保存到: {results_file}")
            
        except Exception as e:
            print(f"❌ 保存测试结果失败: {e}")

if __name__ == "__main__":
    tester = Day8APIFunctionTest()
    tester.run_all_tests()