#!/usr/bin/env python3
"""
Day8 基础功能测试
测试Day8系统的核心功能是否正常工作
"""

import sys
import os
import json
import importlib.util
import traceback
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class Day8BasicFunctionTest:
    """Day8基础功能测试类"""
    
    def __init__(self):
        self.test_results = []
        self.config = self.load_test_config()
        
    def load_test_config(self):
        """加载测试配置"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), 'test_config.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 无法加载测试配置: {e}")
            return {}
    
    def test_module_import(self):
        """测试模块导入"""
        print("🧪 测试1: 模块导入测试")
        try:
            import day8
            self.record_result("模块导入", True, "Day8模块导入成功")
            return day8
        except Exception as e:
            self.record_result("模块导入", False, f"Day8模块导入失败: {str(e)}")
            return None
    
    def test_core_functions_exist(self, day8_module):
        """测试核心函数是否存在"""
        print("🧪 测试2: 核心函数存在性测试")
        
        if not day8_module:
            self.record_result("核心函数检查", False, "模块未导入")
            return
        
        expected_functions = self.config.get('test_scenarios', {}).get('basic_functionality', {}).get('expected_functions', [])
        missing_functions = []
        
        for func_name in expected_functions:
            if hasattr(day8_module, func_name):
                print(f"   ✅ {func_name}: 存在")
            else:
                print(f"   ❌ {func_name}: 缺失")
                missing_functions.append(func_name)
        
        if not missing_functions:
            self.record_result("核心函数检查", True, f"所有{len(expected_functions)}个核心函数都存在")
        else:
            self.record_result("核心函数检查", False, f"缺失函数: {missing_functions}")
    
    def test_config_files_exist(self):
        """测试配置文件是否存在"""
        print("🧪 测试3: 配置文件存在性测试")
        
        required_configs = [
            'config/datasets_config.json',
            'config/operators_config.json', 
            'config/combinations_config.json'
        ]
        
        missing_configs = []
        project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        for config_file in required_configs:
            config_path = os.path.join(project_dir, config_file)
            if os.path.exists(config_path):
                print(f"   ✅ {os.path.basename(config_file)}: 存在")
            else:
                print(f"   ❌ {os.path.basename(config_file)}: 缺失")
                missing_configs.append(os.path.basename(config_file))
        
        if not missing_configs:
            self.record_result("配置文件检查", True, f"所有{len(required_configs)}个配置文件都存在")
        else:
            self.record_result("配置文件检查", False, f"缺失配置文件: {missing_configs}")
    
    def test_manager_classes(self, day8_module):
        """测试管理器类的初始化"""
        print("🧪 测试4: 管理器类初始化测试")
        
        if not day8_module:
            self.record_result("管理器类初始化", False, "模块未导入")
            return
        
        # 临时改变工作目录到项目根目录
        original_cwd = os.getcwd()
        project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        os.chdir(project_dir)
        
        managers = {
            'DatasetManager': 'day8.DatasetManager',
            'OperatorLibrary': 'day8.OperatorLibrary', 
            'CombinationManager': 'day8.CombinationManager',
            'CheckpointManager': 'day8.CheckpointManager',
            'ProgressTracker': 'day8.ProgressTracker'
        }
        
        failed_managers = []
        
        try:
            for manager_name, manager_class in managers.items():
                try:
                    manager_cls = getattr(day8_module, manager_name)
                    if manager_name in ['CheckpointManager', 'ProgressTracker']:
                        # 这些类不需要配置文件
                        instance = manager_cls()
                    else:
                        # 这些类需要配置文件
                        instance = manager_cls()
                    print(f"   ✅ {manager_name}: 初始化成功")
                except Exception as e:
                    print(f"   ❌ {manager_name}: 初始化失败 - {str(e)}")
                    failed_managers.append(manager_name)
        finally:
            # 恢复原工作目录
            os.chdir(original_cwd)
        
        if not failed_managers:
            self.record_result("管理器类初始化", True, f"所有{len(managers)}个管理器类初始化成功")
        else:
            self.record_result("管理器类初始化", False, f"失败的管理器: {failed_managers}")
    
    def test_checkpoint_system(self):
        """测试断点系统"""
        print("🧪 测试5: 断点系统测试")
        
        try:
            # 测试断点目录创建
            project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            checkpoint_dir = os.path.join(project_dir, 'checkpoints')
            
            if not os.path.exists(checkpoint_dir):
                os.makedirs(checkpoint_dir)
            
            # 测试断点文件路径
            checkpoint_file = os.path.join(checkpoint_dir, 'session_checkpoint.pkl')
            progress_file = os.path.join(checkpoint_dir, 'progress_stats.json')
            
            self.record_result("断点系统", True, f"断点目录存在: {checkpoint_dir}")
            
        except Exception as e:
            self.record_result("断点系统", False, f"断点系统测试失败: {str(e)}")
    
    def test_quality_standards(self):
        """测试质量检测标准"""
        print("🧪 测试6: 质量检测标准测试")
        
        expected_standards = self.config.get('test_scenarios', {}).get('quality_detection', {}).get('quality_standards', [])
        
        # 这里我们检查代码中是否包含相关的质量检测逻辑
        try:
            project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            day8_path = os.path.join(project_dir, 'day8.py')
            
            with open(day8_path, 'r', encoding='utf-8') as f:
                day8_content = f.read()
            
            found_standards = []
            for standard in expected_standards:
                if standard in day8_content:
                    found_standards.append(standard)
                    print(f"   ✅ {standard}: 在代码中找到")
                else:
                    print(f"   ❌ {standard}: 在代码中未找到")
            
            if len(found_standards) == len(expected_standards):
                self.record_result("质量检测标准", True, f"所有{len(expected_standards)}个质量标准都在代码中找到")
            else:
                self.record_result("质量检测标准", False, f"找到{len(found_standards)}/{len(expected_standards)}个质量标准")
                
        except Exception as e:
            self.record_result("质量检测标准", False, f"质量检测标准测试失败: {str(e)}")
    
    def record_result(self, test_name, success, message):
        """记录测试结果"""
        self.test_results.append({
            'test_name': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat()
        })
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始Day8基础功能测试")
        print("=" * 60)
        
        # 测试模块导入
        day8_module = self.test_module_import()
        
        # 测试核心函数
        self.test_core_functions_exist(day8_module)
        
        # 测试配置文件
        self.test_config_files_exist()
        
        # 测试管理器类
        self.test_manager_classes(day8_module)
        
        # 测试断点系统
        self.test_checkpoint_system()
        
        # 测试质量检测标准
        self.test_quality_standards()
        
        # 输出测试结果
        self.print_summary()
        
        # 保存测试结果
        self.save_results()
    
    def print_summary(self):
        """打印测试摘要"""
        print("\n" + "=" * 60)
        print("📊 测试结果摘要")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests} ✅")
        print(f"失败测试: {failed_tests} ❌")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        print("\n📋 详细结果:")
        for result in self.test_results:
            status = "✅" if result['success'] else "❌"
            print(f"{status} {result['test_name']}: {result['message']}")
    
    def save_results(self):
        """保存测试结果"""
        try:
            test_dir = os.path.dirname(__file__)
            results_file = os.path.join(test_dir, 'basic_function_test_results.json')
            
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'test_info': {
                        'test_type': 'basic_functionality',
                        'test_time': datetime.now().isoformat(),
                        'total_tests': len(self.test_results),
                        'passed_tests': sum(1 for result in self.test_results if result['success']),
                        'failed_tests': sum(1 for result in self.test_results if not result['success'])
                    },
                    'test_results': self.test_results
                }, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 测试结果已保存到: {results_file}")
            
        except Exception as e:
            print(f"❌ 保存测试结果失败: {e}")

if __name__ == "__main__":
    tester = Day8BasicFunctionTest()
    tester.run_all_tests()