{"test_info": {"test_type": "full_integration", "test_time": "2025-09-10T11:19:01.087350", "total_tests": 7, "passed_tests": 2, "failed_tests": 5, "success_rate": 28.57142857142857}, "test_results": [{"test_name": "完整系统初始化", "success": false, "message": "系统初始化异常: 无法加载数据集配置", "timestamp": "2025-09-10T11:18:52.423699"}, {"test_name": "数据集选择流程", "success": false, "message": "数据集管理器未初始化", "timestamp": "2025-09-10T11:18:52.423729"}, {"test_name": "组合选择流程", "success": false, "message": "组合管理器未初始化", "timestamp": "2025-09-10T11:18:52.423741"}, {"test_name": "Alpha生成流程", "success": true, "message": "Alpha生成流程完整，生成了3个策略", "timestamp": "2025-09-10T11:19:01.086565"}, {"test_name": "质量检测系统", "success": true, "message": "质量检测系统正常，检测了7个标准", "timestamp": "2025-09-10T11:19:01.086734"}, {"test_name": "文件输出系统", "success": false, "message": "文件输出系统异常: [Errno 2] No such file or directory: 'data/qualified_alphas.txt'", "timestamp": "2025-09-10T11:19:01.086932"}, {"test_name": "断点集成功能", "success": false, "message": "断点或进度管理器未初始化", "timestamp": "2025-09-10T11:19:01.086950"}]}