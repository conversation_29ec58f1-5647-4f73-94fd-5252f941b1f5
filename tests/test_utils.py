#!/usr/bin/env python3
"""
测试工具类
为测试提供公共功能
"""

import os
import sys
from contextlib import contextmanager

class TestUtils:
    """测试工具类"""
    
    @staticmethod
    @contextmanager
    def project_working_directory():
        """上下文管理器：临时切换到项目根目录"""
        original_cwd = os.getcwd()
        # 获取项目根目录（从tests目录向上一级）
        project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        try:
            os.chdir(project_dir)
            yield project_dir
        finally:
            os.chdir(original_cwd)
    
    @staticmethod
    def add_project_to_path():
        """将项目根目录添加到Python路径"""
        project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if project_dir not in sys.path:
            sys.path.insert(0, project_dir)
        return project_dir
    
    @staticmethod
    def get_project_dir():
        """获取项目根目录"""
        return os.path.dirname(os.path.dirname(os.path.abspath(__file__)))