#!/usr/bin/env python3
"""
Day8 测试套件运行器
一键运行所有测试并生成总体报告
"""

import sys
import os
import json
import subprocess
import time
from datetime import datetime
import importlib.util

class Day8TestRunner:
    """Day8测试套件运行器"""
    
    def __init__(self):
        self.test_results = {}
        self.test_dir = os.path.dirname(os.path.abspath(__file__))
        self.project_dir = os.path.dirname(self.test_dir)
        
    def run_test_file(self, test_file, test_name):
        """运行单个测试文件"""
        print(f"\n{'='*60}")
        print(f"🧪 运行测试: {test_name}")
        print(f"{'='*60}")
        
        test_path = os.path.join(self.test_dir, test_file)
        
        try:
            start_time = time.time()
            
            # 使用subprocess运行测试
            result = subprocess.run(
                [sys.executable, test_path],
                capture_output=True,
                text=True,
                cwd=self.project_dir
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 输出测试过程
            if result.stdout:
                print(result.stdout)
            
            if result.stderr and result.stderr.strip():
                print("stderr:")
                print(result.stderr)
            
            # 尝试读取测试结果文件
            results_file = test_file.replace('.py', '_results.json')
            results_path = os.path.join(self.test_dir, results_file)
            
            test_details = None
            if os.path.exists(results_path):
                try:
                    with open(results_path, 'r', encoding='utf-8') as f:
                        test_details = json.load(f)
                except:
                    pass
            
            # 记录测试结果
            self.test_results[test_name] = {
                'success': result.returncode == 0,
                'duration': duration,
                'return_code': result.returncode,
                'stdout_lines': len(result.stdout.split('\n')) if result.stdout else 0,
                'stderr_lines': len(result.stderr.split('\n')) if result.stderr else 0,
                'details': test_details,
                'timestamp': datetime.now().isoformat()
            }
            
            if result.returncode == 0:
                print(f"\n✅ {test_name} 测试完成 (耗时: {duration:.1f}秒)")
            else:
                print(f"\n❌ {test_name} 测试失败 (返回码: {result.returncode})")
                
        except Exception as e:
            print(f"\n❌ 运行{test_name}测试时出错: {str(e)}")
            self.test_results[test_name] = {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def run_basic_tests(self):
        """运行基础功能测试"""
        self.run_test_file('test_basic_functionality.py', '基础功能测试')
    
    def run_api_tests(self):
        """运行API功能测试"""
        self.run_test_file('test_api_functionality.py', 'API功能测试')
    
    def run_checkpoint_tests(self):
        """运行断点恢复测试"""
        self.run_test_file('test_checkpoint_recovery.py', '断点恢复测试')
    
    def run_integration_tests(self):
        """运行集成测试"""
        print("\n⚠️  集成测试将进行真实的API调用，需要有效的网络连接和认证")
        response = input("是否继续运行集成测试？ (y/N): ").strip().lower()
        
        if response == 'y':
            self.run_test_file('test_integration.py', '集成测试')
        else:
            print("🔄 跳过集成测试")
            self.test_results['集成测试'] = {
                'success': None,
                'skipped': True,
                'reason': '用户跳过',
                'timestamp': datetime.now().isoformat()
            }
    
    def generate_summary_report(self):
        """生成总体测试报告"""
        print(f"\n{'='*60}")
        print("📊 Day8 测试套件总体报告")
        print(f"{'='*60}")
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() 
                          if result.get('success') == True)
        failed_tests = sum(1 for result in self.test_results.values() 
                          if result.get('success') == False)
        skipped_tests = sum(1 for result in self.test_results.values() 
                           if result.get('skipped') == True)
        
        print(f"测试总数: {total_tests}")
        print(f"通过测试: {passed_tests} ✅")
        print(f"失败测试: {failed_tests} ❌")
        print(f"跳过测试: {skipped_tests} ⏭️")
        
        if total_tests > 0:
            success_rate = passed_tests / (total_tests - skipped_tests) * 100 if (total_tests - skipped_tests) > 0 else 0
            print(f"成功率: {success_rate:.1f}%")
        
        print(f"\n📋 详细测试结果:")
        for test_name, result in self.test_results.items():
            if result.get('skipped'):
                print(f"⏭️ {test_name}: 跳过 ({result.get('reason', '未知原因')})")
            elif result.get('success'):
                duration = result.get('duration', 0)
                print(f"✅ {test_name}: 通过 (耗时: {duration:.1f}秒)")
                
                # 显示详细测试数据
                details = result.get('details')
                if details and 'test_info' in details:
                    test_info = details['test_info']
                    total = test_info.get('total_tests', 0)
                    passed = test_info.get('passed_tests', 0)
                    print(f"   📊 子测试: {passed}/{total} 通过")
            else:
                error = result.get('error', '未知错误')
                return_code = result.get('return_code', 'N/A')
                print(f"❌ {test_name}: 失败 (返回码: {return_code})")
                if error != '未知错误':
                    print(f"   错误: {error}")
        
        # 总体评估
        print(f"\n🎯 总体评估:")
        if failed_tests == 0 and passed_tests > 0:
            print("🎉 所有测试通过！Day8系统功能完整，可以正常使用。")
        elif passed_tests >= (total_tests - skipped_tests) * 0.8:
            print("✅ 大部分测试通过，Day8系统基本功能正常。")
            if failed_tests > 0:
                print("⚠️ 建议检查失败的测试项目。")
        else:
            print("⚠️ 多个测试失败，Day8系统可能存在问题，建议进行修复。")
        
        # 使用建议
        print(f"\n📝 使用建议:")
        if passed_tests >= 3:
            print("✅ Day8系统可以安全使用")
            print("📖 请参考README.md了解详细使用方法")
            print("🚀 运行 python3 day8.py 开始使用系统")
        else:
            print("⚠️ 建议先修复测试失败的问题再使用系统")
            print("📋 查看测试结果了解具体问题")
    
    def save_summary_report(self):
        """保存总体测试报告"""
        try:
            report_data = {
                'test_suite_info': {
                    'name': 'Day8 Alpha策略智能生成系统测试套件',
                    'version': '1.0',
                    'test_time': datetime.now().isoformat(),
                    'total_tests': len(self.test_results),
                    'passed_tests': sum(1 for result in self.test_results.values() 
                                      if result.get('success') == True),
                    'failed_tests': sum(1 for result in self.test_results.values() 
                                      if result.get('success') == False),
                    'skipped_tests': sum(1 for result in self.test_results.values() 
                                       if result.get('skipped') == True)
                },
                'test_results': self.test_results
            }
            
            summary_file = os.path.join(self.test_dir, 'test_suite_summary.json')
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 总体测试报告已保存到: {summary_file}")
            
        except Exception as e:
            print(f"❌ 保存总体测试报告失败: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始运行Day8完整测试套件")
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        start_time = time.time()
        
        # 运行各种测试
        self.run_basic_tests()
        self.run_api_tests() 
        self.run_checkpoint_tests()
        self.run_integration_tests()
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        print(f"\n⏱️ 测试套件总耗时: {total_duration:.1f}秒")
        
        # 生成和保存报告
        self.generate_summary_report()
        self.save_summary_report()

def main():
    """主函数"""
    print("🧪 Day8 Alpha策略智能生成系统 - 测试套件")
    print("=" * 60)
    print("此测试套件将验证Day8系统的所有核心功能")
    print("包括：基础功能、API调用、断点恢复、完整集成")
    print("=" * 60)
    
    runner = Day8TestRunner()
    runner.run_all_tests()

if __name__ == "__main__":
    main()