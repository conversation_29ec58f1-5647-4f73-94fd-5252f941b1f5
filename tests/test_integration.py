#!/usr/bin/env python3
"""
Day8 完整集成测试
模拟真实的Alpha策略生成流程进行端到端测试
"""

import sys
import os
import json
import time
import threading
import signal
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class Day8IntegrationTest:
    """Day8完整集成测试类"""
    
    def __init__(self):
        self.test_results = []
        self.session = None
        self.managers = {}
        
    def test_full_initialization(self):
        """测试完整系统初始化"""
        print("🧪 测试1: 完整系统初始化测试")
        
        try:
            import day8
            
            # 测试断点系统初始化
            day8.setup_checkpoint_system()
            
            # 测试API认证
            self.session = day8.sign_in()
            
            # 测试管理器初始化
            self.managers['dataset'] = day8.DatasetManager()
            self.managers['operator'] = day8.OperatorLibrary()
            self.managers['combination'] = day8.CombinationManager()
            self.managers['checkpoint'] = day8.CheckpointManager()
            self.managers['progress'] = day8.ProgressTracker()
            
            # 测试日志系统初始化
            detailed_logger = day8.setup_logging()
            
            if all([
                self.session,
                all(self.managers.values()),
                detailed_logger
            ]):
                self.record_result("完整系统初始化", True, "所有系统组件初始化成功")
            else:
                self.record_result("完整系统初始化", False, "部分系统组件初始化失败")
                
        except Exception as e:
            self.record_result("完整系统初始化", False, f"系统初始化异常: {str(e)}")
    
    def test_dataset_selection_flow(self):
        """测试数据集选择流程"""
        print("🧪 测试2: 数据集选择流程测试")
        
        if 'dataset' not in self.managers:
            self.record_result("数据集选择流程", False, "数据集管理器未初始化")
            return
        
        try:
            dataset_manager = self.managers['dataset']
            
            # 测试数据集列表获取
            datasets = dataset_manager.datasets
            if len(datasets) >= 10:  # 期望至少10个数据集
                print(f"   ✅ 获取到{len(datasets)}个数据集配置")
                
                # 测试选择一个数据集（模拟用户选择analyst4）
                dataset_id, search_scope, config = dataset_manager.get_dataset_by_choice("1")
                
                if dataset_id and search_scope and config:
                    print(f"   ✅ 数据集选择成功: {dataset_id}")
                    self.record_result("数据集选择流程", True, f"数据集选择流程正常，选择了{dataset_id}")
                else:
                    self.record_result("数据集选择流程", False, "数据集选择返回空值")
            else:
                self.record_result("数据集选择流程", False, f"数据集数量不足，只有{len(datasets)}个")
                
        except Exception as e:
            self.record_result("数据集选择流程", False, f"数据集选择流程异常: {str(e)}")
    
    def test_combination_selection_flow(self):
        """测试组合选择流程"""
        print("🧪 测试3: 组合选择流程测试")
        
        if 'combination' not in self.managers:
            self.record_result("组合选择流程", False, "组合管理器未初始化")
            return
        
        try:
            combination_manager = self.managers['combination']
            
            # 测试组合列表获取
            combinations = combination_manager.combination_sets
            if len(combinations) >= 5:  # 期望至少5个组合
                print(f"   ✅ 获取到{len(combinations)}个组合配置")
                
                # 测试选择一个组合（模拟用户选择趋势跟踪）
                combo_id, combo_config = combination_manager.get_combination_by_choice("1")
                
                if combo_id and combo_config:
                    print(f"   ✅ 组合选择成功: {combo_config.get('name', combo_id)}")
                    self.record_result("组合选择流程", True, f"组合选择流程正常，选择了{combo_config.get('name', combo_id)}")
                else:
                    self.record_result("组合选择流程", False, "组合选择返回空值")
            else:
                self.record_result("组合选择流程", False, f"组合数量不足，只有{len(combinations)}个")
                
        except Exception as e:
            self.record_result("组合选择流程", False, f"组合选择流程异常: {str(e)}")
    
    def test_alpha_generation_flow(self):
        """测试Alpha生成流程"""
        print("🧪 测试4: Alpha生成流程测试")
        
        if not self.session:
            self.record_result("Alpha生成流程", False, "会话未建立")
            return
        
        try:
            import day8
            
            # 使用analyst4数据集进行测试
            search_scope = {
                'instrumentType': 'EQUITY',
                'region': 'USA',
                'delay': '0',
                'universe': 'TOP3000'
            }
            
            # 获取数据字段
            print("   🔍 获取数据字段...")
            datafields_df = day8.get_datafields(self.session, search_scope, 'analyst4')
            
            if not datafields_df.empty:
                matrix_fields = datafields_df[datafields_df['type'] == "MATRIX"]
                if len(matrix_fields) >= 3:
                    datafields_list = matrix_fields['id'].values[:3]  # 只取前3个字段
                    print(f"   ✅ 获取到{len(datafields_list)}个测试字段")
                    
                    # 使用趋势跟踪组合配置
                    combination_config = {
                        'name': '测试趋势跟踪组合',
                        'group_ops': ['group_rank'],
                        'ts_ops': ['ts_mean'],
                        'periods': [10],
                        'groups': ['subindustry']
                    }
                    
                    # 生成Alpha表达式
                    print("   🔧 生成Alpha表达式...")
                    alpha_expressions = day8.generate_alpha_expressions_with_combination(
                        datafields_list, combination_config
                    )
                    
                    if len(alpha_expressions) >= 3:
                        print(f"   ✅ 生成了{len(alpha_expressions)}个Alpha表达式")
                        
                        # 封装Alpha策略
                        print("   📦 封装Alpha策略...")
                        alpha_list = day8.create_alpha_list(alpha_expressions, search_scope)
                        
                        if len(alpha_list) == len(alpha_expressions):
                            print(f"   ✅ 封装了{len(alpha_list)}个Alpha策略")
                            self.record_result("Alpha生成流程", True, f"Alpha生成流程完整，生成了{len(alpha_list)}个策略")
                        else:
                            self.record_result("Alpha生成流程", False, "Alpha策略封装数量不匹配")
                    else:
                        self.record_result("Alpha生成流程", False, f"Alpha表达式生成数量不足: {len(alpha_expressions)}")
                else:
                    self.record_result("Alpha生成流程", False, "MATRIX字段数量不足")
            else:
                self.record_result("Alpha生成流程", False, "未获取到数据字段")
                
        except Exception as e:
            self.record_result("Alpha生成流程", False, f"Alpha生成流程异常: {str(e)}")
    
    def test_quality_detection_system(self):
        """测试质量检测系统"""
        print("🧪 测试5: 质量检测系统测试")
        
        try:
            import day8
            
            # 构造一个模拟的Alpha数据用于测试
            mock_alpha_data = {
                'is': {
                    'sharpe': 1.5,
                    'fitness': 1.2,
                    'turnover': 0.05,  # 5%
                    'subUniverseSharpe': 0.1,
                    'maxWeight': 0.08,  # 8%
                    'ic_mean': 0.02,
                    'returns': 0.12
                }
            }
            
            # 测试质量检测
            is_qualified, quality_result = day8.check_alpha_quality(mock_alpha_data)
            
            # 检查返回结果结构
            expected_keys = ['qualified', 'passed_checks', 'total_checks', 'metrics', 'checks']
            if all(key in quality_result for key in expected_keys):
                print("   ✅ 质量检测返回结构正确")
                
                # 检查7个标准检测
                if quality_result['total_checks'] == 7:
                    print("   ✅ 7个质量标准都被检测")
                    
                    # 检查指标提取
                    metrics = quality_result['metrics']
                    if all(key in metrics for key in ['sharpe', 'fitness', 'turnover']):
                        print("   ✅ 关键指标提取正确")
                        self.record_result("质量检测系统", True, f"质量检测系统正常，检测了{quality_result['total_checks']}个标准")
                    else:
                        self.record_result("质量检测系统", False, "关键指标提取不完整")
                else:
                    self.record_result("质量检测系统", False, f"质量标准数量错误: {quality_result['total_checks']}")
            else:
                self.record_result("质量检测系统", False, "质量检测返回结构不正确")
                
        except Exception as e:
            self.record_result("质量检测系统", False, f"质量检测系统异常: {str(e)}")
    
    def test_file_output_system(self):
        """测试文件输出系统"""
        print("🧪 测试6: 文件输出系统测试")
        
        try:
            import day8
            
            # 构造测试数据
            alpha_id = "TEST123"
            alpha_expression = "group_rank(ts_mean(test_field, 10), subindustry)"
            quality_result = {
                'qualified': True,
                'passed_checks': 7,
                'total_checks': 7,
                'metrics': {
                    'sharpe': 1.5,
                    'fitness': 1.2,
                    'turnover': 5.0,
                    'sub_universe_sharpe': 0.1,
                    'weight_concentration': 8.0,
                    'ic_mean': 0.02,
                    'returns': 0.12
                },
                'checks': {}
            }
            
            # 测试保存合格Alpha
            project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            qualified_file = os.path.join(project_dir, 'qualified_alphas.txt')
            success_file = os.path.join(project_dir, 'successful_alpha_ids.txt')
            
            # 备份现有文件
            backup_files = []
            for file_path in [qualified_file, success_file]:
                if os.path.exists(file_path):
                    backup_path = file_path + '.backup'
                    os.rename(file_path, backup_path)
                    backup_files.append((file_path, backup_path))
            
            try:
                # 保存测试Alpha
                day8.save_qualified_alpha(alpha_id, alpha_expression, quality_result, 1, "测试组合")
                
                # 检查文件是否生成
                files_created = []
                if os.path.exists(qualified_file):
                    files_created.append("qualified_alphas.txt")
                    print("   ✅ qualified_alphas.txt 创建成功")
                
                if os.path.exists(success_file):
                    files_created.append("successful_alpha_ids.txt")
                    print("   ✅ successful_alpha_ids.txt 创建成功")
                
                if len(files_created) == 2:
                    self.record_result("文件输出系统", True, "所有输出文件创建成功")
                else:
                    self.record_result("文件输出系统", False, f"只创建了{len(files_created)}/2个文件")
                
                # 清理测试文件
                for file_path in [qualified_file, success_file]:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                
            finally:
                # 恢复备份文件
                for original_path, backup_path in backup_files:
                    if os.path.exists(backup_path):
                        os.rename(backup_path, original_path)
                
        except Exception as e:
            self.record_result("文件输出系统", False, f"文件输出系统异常: {str(e)}")
    
    def test_checkpoint_integration(self):
        """测试断点集成功能"""
        print("🧪 测试7: 断点集成功能测试")
        
        if 'checkpoint' not in self.managers or 'progress' not in self.managers:
            self.record_result("断点集成功能", False, "断点或进度管理器未初始化")
            return
        
        try:
            checkpoint_manager = self.managers['checkpoint']
            progress_tracker = self.managers['progress']
            
            # 模拟一个处理过程
            progress_tracker.start_batch(5)
            progress_tracker.record_submission(15.0, True)
            progress_tracker.record_submission(18.0, True)
            
            # 保存断点
            checkpoint_manager.save_checkpoint("集成测试断点")
            
            # 检查断点文件
            project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            checkpoint_file = os.path.join(project_dir, 'checkpoints', 'session_checkpoint.pkl')
            
            if os.path.exists(checkpoint_file):
                print("   ✅ 断点文件创建成功")
                
                # 测试断点加载
                loaded_checkpoint = checkpoint_manager.load_checkpoint()
                if loaded_checkpoint and loaded_checkpoint.get('reason') == "集成测试断点":
                    print("   ✅ 断点加载成功")
                    self.record_result("断点集成功能", True, "断点集成功能正常")
                else:
                    self.record_result("断点集成功能", False, "断点加载失败或内容不正确")
            else:
                self.record_result("断点集成功能", False, "断点文件创建失败")
                
        except Exception as e:
            self.record_result("断点集成功能", False, f"断点集成功能异常: {str(e)}")
    
    def record_result(self, test_name, success, message):
        """记录测试结果"""
        self.test_results.append({
            'test_name': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat()
        })
    
    def run_all_tests(self):
        """运行所有集成测试"""
        print("🚀 开始Day8完整集成测试")
        print("⚠️  此测试将进行真实的API调用，请确保网络连接正常")
        print("=" * 60)
        
        # 完整系统初始化测试
        self.test_full_initialization()
        
        # 数据集选择流程测试
        self.test_dataset_selection_flow()
        
        # 组合选择流程测试
        self.test_combination_selection_flow()
        
        # Alpha生成流程测试（需要网络连接）
        print("\n⚠️  即将进行需要网络连接的测试...")
        time.sleep(2)
        self.test_alpha_generation_flow()
        
        # 质量检测系统测试
        self.test_quality_detection_system()
        
        # 文件输出系统测试
        self.test_file_output_system()
        
        # 断点集成功能测试
        self.test_checkpoint_integration()
        
        # 输出测试结果
        self.print_summary()
        
        # 保存测试结果
        self.save_results()
    
    def print_summary(self):
        """打印测试摘要"""
        print("\n" + "=" * 60)
        print("📊 完整集成测试结果摘要")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests} ✅")
        print(f"失败测试: {failed_tests} ❌")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        print("\n📋 详细结果:")
        for result in self.test_results:
            status = "✅" if result['success'] else "❌"
            print(f"{status} {result['test_name']}: {result['message']}")
        
        # 总体评估
        if passed_tests == total_tests:
            print("\n🎉 所有测试通过！Day8系统功能完整。")
        elif passed_tests >= total_tests * 0.8:
            print("\n✅ 大部分测试通过，Day8系统基本功能正常。")
        else:
            print("\n⚠️ 多个测试失败，Day8系统可能存在问题。")
    
    def save_results(self):
        """保存测试结果"""
        try:
            test_dir = os.path.dirname(__file__)
            results_file = os.path.join(test_dir, 'integration_test_results.json')
            
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'test_info': {
                        'test_type': 'full_integration',
                        'test_time': datetime.now().isoformat(),
                        'total_tests': len(self.test_results),
                        'passed_tests': sum(1 for result in self.test_results if result['success']),
                        'failed_tests': sum(1 for result in self.test_results if not result['success']),
                        'success_rate': sum(1 for result in self.test_results if result['success']) / len(self.test_results) * 100
                    },
                    'test_results': self.test_results
                }, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 测试结果已保存到: {results_file}")
            
        except Exception as e:
            print(f"❌ 保存测试结果失败: {e}")

if __name__ == "__main__":
    tester = Day8IntegrationTest()
    tester.run_all_tests()