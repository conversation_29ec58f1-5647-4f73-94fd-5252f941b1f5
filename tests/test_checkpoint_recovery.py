#!/usr/bin/env python3
"""
Day8 断点恢复功能测试
测试Day8系统的断点恢复和智能进度监控功能
"""

import sys
import os
import json
import pickle
import time
import shutil
from datetime import datetime, timedelta, timedelta

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class Day8CheckpointTest:
    """Day8断点恢复功能测试类"""
    
    def __init__(self):
        self.test_results = []
        self.project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.checkpoint_dir = os.path.join(self.project_dir, 'checkpoints')
        
    def test_checkpoint_directory_creation(self):
        """测试断点目录创建"""
        print("🧪 测试1: 断点目录创建测试")
        
        try:
            import day8
            
            # 如果目录存在，先删除
            if os.path.exists(self.checkpoint_dir):
                shutil.rmtree(self.checkpoint_dir)
            
            # 测试断点系统初始化
            day8.setup_checkpoint_system()
            
            if os.path.exists(self.checkpoint_dir):
                self.record_result("断点目录创建", True, f"断点目录创建成功: {self.checkpoint_dir}")
            else:
                self.record_result("断点目录创建", False, "断点目录创建失败")
                
        except Exception as e:
            self.record_result("断点目录创建", False, f"断点目录创建异常: {str(e)}")
    
    def test_checkpoint_manager_basic(self):
        """测试断点管理器基础功能"""
        print("🧪 测试2: 断点管理器基础功能测试")
        
        try:
            import day8
            
            # 创建断点管理器
            checkpoint_manager = day8.CheckpointManager()
            
            # 测试保存断点
            checkpoint_manager.save_checkpoint("测试保存")
            
            checkpoint_file = os.path.join(self.checkpoint_dir, "session_checkpoint.pkl")
            if os.path.exists(checkpoint_file):
                print("   ✅ 断点文件保存成功")
                
                # 测试加载断点
                loaded_checkpoint = checkpoint_manager.load_checkpoint()
                if loaded_checkpoint:
                    print("   ✅ 断点文件加载成功")
                    print(f"   📋 断点信息: {loaded_checkpoint.get('reason', 'Unknown')}")
                    self.record_result("断点管理器基础功能", True, "保存和加载断点成功")
                else:
                    self.record_result("断点管理器基础功能", False, "断点加载失败")
            else:
                self.record_result("断点管理器基础功能", False, "断点文件保存失败")
                
        except Exception as e:
            self.record_result("断点管理器基础功能", False, f"断点管理器测试异常: {str(e)}")
    
    def test_checkpoint_expiration(self):
        """测试断点过期机制"""
        print("🧪 测试3: 断点过期机制测试")
        
        try:
            import day8
            
            checkpoint_manager = day8.CheckpointManager()
            checkpoint_file = os.path.join(self.checkpoint_dir, "session_checkpoint.pkl")
            
            # 创建一个过期的断点（修改时间戳）
            old_checkpoint = {
                'timestamp': (datetime.now() - timedelta(days=2)).isoformat(),
                'reason': '过期测试',
                'session_valid': False,
                'progress_data': {}
            }
            
            with open(checkpoint_file, 'wb') as f:
                pickle.dump(old_checkpoint, f)
            
            # 尝试加载过期断点
            loaded_checkpoint = checkpoint_manager.load_checkpoint()
            
            if loaded_checkpoint is None:
                self.record_result("断点过期机制", True, "过期断点正确被忽略")
            else:
                self.record_result("断点过期机制", False, "过期断点未被正确处理")
                
        except Exception as e:
            self.record_result("断点过期机制", False, f"断点过期机制测试异常: {str(e)}")
    
    def test_progress_tracker_basic(self):
        """测试进度追踪器基础功能"""
        print("🧪 测试4: 进度追踪器基础功能测试")
        
        try:
            import day8
            
            # 创建进度追踪器
            progress_tracker = day8.ProgressTracker()
            
            # 测试开始批次
            progress_tracker.start_batch(10)
            
            if progress_tracker.total_planned == 10:
                print("   ✅ 批次开始成功")
                
                # 测试记录API响应时间
                progress_tracker.record_api_response(2.5)
                progress_tracker.record_api_response(3.0)
                progress_tracker.record_api_response(2.8)
                
                if len(progress_tracker.api_response_times) == 3:
                    print("   ✅ API响应时间记录成功")
                    
                    # 测试记录提交结果
                    progress_tracker.record_submission(15.0, True)
                    progress_tracker.record_submission(18.0, True)
                    progress_tracker.record_submission(12.0, False)
                    
                    if progress_tracker.completed == 2 and progress_tracker.failed == 1:
                        print("   ✅ 提交结果记录成功")
                        self.record_result("进度追踪器基础功能", True, "进度追踪器所有基础功能正常")
                    else:
                        self.record_result("进度追踪器基础功能", False, "提交结果记录不正确")
                else:
                    self.record_result("进度追踪器基础功能", False, "API响应时间记录失败")
            else:
                self.record_result("进度追踪器基础功能", False, "批次开始失败")
                
        except Exception as e:
            self.record_result("进度追踪器基础功能", False, f"进度追踪器测试异常: {str(e)}")
    
    def test_progress_stats_persistence(self):
        """测试进度统计持久化"""
        print("🧪 测试5: 进度统计持久化测试")
        
        try:
            import day8
            
            progress_tracker = day8.ProgressTracker()
            
            # 设置一些测试数据
            progress_tracker.avg_response_time = 3.5
            progress_tracker.avg_processing_time = 20.0
            progress_tracker.success_rate = 0.8
            
            # 保存统计数据
            progress_tracker.save_progress_stats()
            
            progress_stats_file = os.path.join(self.checkpoint_dir, "progress_stats.json")
            if os.path.exists(progress_stats_file):
                print("   ✅ 进度统计文件保存成功")
                
                # 创建新的进度追踪器并加载数据
                new_tracker = day8.ProgressTracker()
                
                if (abs(new_tracker.avg_response_time - 3.5) < 0.1 and
                    abs(new_tracker.avg_processing_time - 20.0) < 0.1 and
                    abs(new_tracker.success_rate - 0.8) < 0.1):
                    print("   ✅ 进度统计数据加载成功")
                    self.record_result("进度统计持久化", True, "进度统计数据保存和加载成功")
                else:
                    self.record_result("进度统计持久化", False, "进度统计数据加载不正确")
            else:
                self.record_result("进度统计持久化", False, "进度统计文件保存失败")
                
        except Exception as e:
            self.record_result("进度统计持久化", False, f"进度统计持久化测试异常: {str(e)}")
    
    def test_checkpoint_save_conditions(self):
        """测试断点保存条件"""
        print("🧪 测试6: 断点保存条件测试")
        
        try:
            import day8
            
            checkpoint_manager = day8.CheckpointManager()
            # 设置初始保存时间，避免时间条件干扰
            checkpoint_manager.last_save_time = datetime.now()
            
            # 测试不同的保存条件
            conditions = [
                (10, True, "每10个Alpha保存"),
                (20, True, "每10个Alpha保存"),
                (5, False, "不满足保存条件"),
                (30, True, "每10个Alpha保存")
            ]
            
            correct_results = 0
            total_tests = len(conditions)
            
            for completed_count, expected, description in conditions:
                result = checkpoint_manager.should_save_checkpoint(completed_count)
                if result == expected:
                    correct_results += 1
                    print(f"   ✅ {description}: 正确 ({result})")
                else:
                    print(f"   ❌ {description}: 错误 (期望{expected}, 得到{result})")
            
            if correct_results == total_tests:
                self.record_result("断点保存条件", True, f"所有{total_tests}个保存条件测试正确")
            else:
                self.record_result("断点保存条件", False, f"只有{correct_results}/{total_tests}个保存条件测试正确")
                
        except Exception as e:
            self.record_result("断点保存条件", False, f"断点保存条件测试异常: {str(e)}")
    
    def test_checkpoint_cleanup(self):
        """测试断点清理功能"""
        print("🧪 测试7: 断点清理功能测试")
        
        try:
            import day8
            
            checkpoint_manager = day8.CheckpointManager()
            
            # 先保存一个断点
            checkpoint_manager.save_checkpoint("清理测试")
            
            checkpoint_file = os.path.join(self.checkpoint_dir, "session_checkpoint.pkl")
            if os.path.exists(checkpoint_file):
                print("   ✅ 断点文件存在")
                
                # 清理断点
                checkpoint_manager.clear_checkpoint()
                
                if not os.path.exists(checkpoint_file):
                    print("   ✅ 断点文件清理成功")
                    self.record_result("断点清理功能", True, "断点清理功能正常")
                else:
                    self.record_result("断点清理功能", False, "断点文件清理失败")
            else:
                self.record_result("断点清理功能", False, "测试断点文件创建失败")
                
        except Exception as e:
            self.record_result("断点清理功能", False, f"断点清理功能测试异常: {str(e)}")
    
    def record_result(self, test_name, success, message):
        """记录测试结果"""
        self.test_results.append({
            'test_name': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat()
        })
    
    def run_all_tests(self):
        """运行所有断点恢复测试"""
        print("🚀 开始Day8断点恢复功能测试")
        print("=" * 60)
        
        # 断点目录创建测试
        self.test_checkpoint_directory_creation()
        
        # 断点管理器基础功能测试
        self.test_checkpoint_manager_basic()
        
        # 断点过期机制测试
        self.test_checkpoint_expiration()
        
        # 进度追踪器基础功能测试
        self.test_progress_tracker_basic()
        
        # 进度统计持久化测试
        self.test_progress_stats_persistence()
        
        # 断点保存条件测试
        self.test_checkpoint_save_conditions()
        
        # 断点清理功能测试
        self.test_checkpoint_cleanup()
        
        # 输出测试结果
        self.print_summary()
        
        # 保存测试结果
        self.save_results()
    
    def print_summary(self):
        """打印测试摘要"""
        print("\n" + "=" * 60)
        print("📊 断点恢复功能测试结果摘要")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests} ✅")
        print(f"失败测试: {failed_tests} ❌")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        print("\n📋 详细结果:")
        for result in self.test_results:
            status = "✅" if result['success'] else "❌"
            print(f"{status} {result['test_name']}: {result['message']}")
    
    def save_results(self):
        """保存测试结果"""
        try:
            test_dir = os.path.dirname(__file__)
            results_file = os.path.join(test_dir, 'checkpoint_test_results.json')
            
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'test_info': {
                        'test_type': 'checkpoint_recovery',
                        'test_time': datetime.now().isoformat(),
                        'total_tests': len(self.test_results),
                        'passed_tests': sum(1 for result in self.test_results if result['success']),
                        'failed_tests': sum(1 for result in self.test_results if not result['success'])
                    },
                    'test_results': self.test_results
                }, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 测试结果已保存到: {results_file}")
            
        except Exception as e:
            print(f"❌ 保存测试结果失败: {e}")

if __name__ == "__main__":
    tester = Day8CheckpointTest()
    tester.run_all_tests()