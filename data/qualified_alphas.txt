{"alpha_id": "758z6NQ", "expression": "group_rank(ts_mean(fnd6_acdo, 10), subindustry)", "alpha_index": 45, "combination_name": "趋势跟踪组合", "timestamp": "2025-09-04 01:45:37", "qualified": true, "passed_checks": 7, "total_checks": 7, "metrics": {"sharpe": 1.32, "fitness": 1.18, "turnover": 1.49, "sub_universe_sharpe": 0, "weight_concentration": 0, "ic_mean": 0, "returns": 0.1004}, "detailed_checks": {"turnover_above_min": {"passed": true, "message": "Turnover of 1.49% is above cutoff of 1%.", "status": "PASS"}, "turnover_below_max": {"passed": true, "message": "Turnover of 1.49% is below cutoff of 70%.", "status": "PASS"}, "sub_universe_sharpe_ok": {"passed": true, "message": "Sub-universe Sharpe of 0.00 is above cutoff of -1.54.", "status": "PASS"}, "competition_challenge": {"passed": true, "message": "Competition Challenge matches.", "status": "PASS"}, "sharpe_ok": {"passed": true, "message": "Sharpe of 1.32 is above cutoff of 1.25.", "status": "PASS"}, "fitness_ok": {"passed": true, "message": "Fitness of 1.18 is above cutoff of 1.", "status": "PASS"}, "weight_concentration_ok": {"passed": true, "message": "Weight concentration 0% is above cutoff of 10% on 09/04/2025.", "status": "PASS"}}}
{"alpha_id": "WZjAJvG", "expression": "group_rank(ts_mean(fnd6_acdo, 20), subindustry)", "alpha_index": 46, "combination_name": "趋势跟踪组合", "timestamp": "2025-09-04 01:47:07", "qualified": true, "passed_checks": 7, "total_checks": 7, "metrics": {"sharpe": 1.3, "fitness": 1.15, "turnover": 1.46, "sub_universe_sharpe": 0, "weight_concentration": 0, "ic_mean": 0, "returns": 0.0984}, "detailed_checks": {"turnover_above_min": {"passed": true, "message": "Turnover of 1.46% is above cutoff of 1%.", "status": "PASS"}, "turnover_below_max": {"passed": true, "message": "Turnover of 1.46% is below cutoff of 70%.", "status": "PASS"}, "sub_universe_sharpe_ok": {"passed": true, "message": "Sub-universe Sharpe of 0.00 is above cutoff of -1.54.", "status": "PASS"}, "competition_challenge": {"passed": true, "message": "Competition Challenge matches.", "status": "PASS"}, "sharpe_ok": {"passed": true, "message": "Sharpe of 1.30 is above cutoff of 1.25.", "status": "PASS"}, "fitness_ok": {"passed": true, "message": "Fitness of 1.15 is above cutoff of 1.", "status": "PASS"}, "weight_concentration_ok": {"passed": true, "message": "Weight concentration 0% is above cutoff of 10% on 09/04/2025.", "status": "PASS"}}}
{"alpha_id": "KodXJ7x", "expression": "group_rank(ts_sum(fnd6_acdo, 20), subindustry)", "alpha_index": 730, "combination_name": "趋势跟踪组合", "timestamp": "2025-09-04 20:45:56", "qualified": true, "passed_checks": 7, "total_checks": 7, "metrics": {"sharpe": 1.3, "fitness": 1.15, "turnover": 1.46, "sub_universe_sharpe": 0, "weight_concentration": 0, "ic_mean": 0, "returns": 0.0984}, "detailed_checks": {"turnover_above_min": {"passed": true, "message": "Turnover of 1.46% is above cutoff of 1%.", "status": "PASS"}, "turnover_below_max": {"passed": true, "message": "Turnover of 1.46% is below cutoff of 70%.", "status": "PASS"}, "sub_universe_sharpe_ok": {"passed": true, "message": "Sub-universe Sharpe of 0.00 is above cutoff of -1.54.", "status": "PASS"}, "competition_challenge": {"passed": true, "message": "Competition Challenge matches.", "status": "PASS"}, "sharpe_ok": {"passed": true, "message": "Sharpe of 1.30 is above cutoff of 1.25.", "status": "PASS"}, "fitness_ok": {"passed": true, "message": "Fitness of 1.15 is above cutoff of 1.", "status": "PASS"}, "weight_concentration_ok": {"passed": true, "message": "Weight concentration 0% is above cutoff of 10% on 09/04/2025.", "status": "PASS"}}}
