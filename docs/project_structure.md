# Alpha 项目文件结构

## 📁 目录结构

```
/Users/<USER>/Documents/Alpha/alpha/
├── day8.py                          # 🚀 Day8主程序（当前生产版本）
├── README.md                        # 📖 项目主要说明文档
├── checkpoints/                     # 💾 断点恢复目录（Day8特性）
│   ├── session_checkpoint.pkl       # 会话检查点
│   └── progress_stats.json         # 进度统计数据
├── config/                          # ⚙️ 配置文件目录
│   ├── brain.txt                    # API认证信息
│   ├── datasets_config.json         # 数据集配置
│   ├── operators_config.json        # 运算符配置
│   ├── combinations_config.json     # 组合配置（Day7新增）
│   ├── available_datasets.txt       # 可用数据集列表
│   └── operators.txt               # 运算符列表
├── data/                           # 📊 数据文件目录
│   ├── qualified_alphas.txt         # 合格Alpha详细记录
│   └── successful_alpha_ids.txt     # 成功Alpha ID列表
├── docs/                           # 📚 文档目录
│   └── project_structure.md        # 本文件
├── legacy/                         # 📜 历史版本目录
│   ├── day1.ipynb                  # 基础认证和单策略测试
│   ├── day2.ipynb                  # 数据字段查询与探索
│   ├── day3.py                     # 批量策略生成与回测
│   ├── day4.py                     # 运算符库管理优化版本
│   ├── day5.py                     # 配置化架构版本
│   ├── day6.py                     # 数据集分类管理版本
│   └── day7.py                     # 组合策略配置版本
├── logs/                           # 📋 日志文件目录
│   ├── simulation.log              # 基础操作日志
│   └── alpha_test_results.log      # 详细测试结果日志
└── tests/                          # 🧪 测试套件目录（Day8新增）
    ├── README.md                   # 测试说明文档
    ├── test_config.json            # 测试配置文件
    ├── test_basic_functionality.py # 基础功能测试
    ├── test_api_functionality.py  # API功能测试
    ├── test_checkpoint_recovery.py # 断点恢复测试
    ├── test_integration.py         # 完整集成测试
    ├── run_all_tests.py            # 测试运行器
    ├── test_summary_report.md      # 测试总结报告
    └── *_results.json              # 测试结果文件（自动生成）
```

## 🎯 各目录说明

### `/` 根目录
- **day8.py**: 当前生产版本，包含完整的Alpha策略生成系统
- **README.md**: 项目主要说明文档

### `/config/` 配置文件目录
- 包含所有系统配置文件
- API认证信息
- 数据集、运算符、组合等配置

### `/data/` 数据文件目录
- 存储系统生成的数据文件
- 合格Alpha记录
- 成功Alpha ID列表

### `/docs/` 文档目录
- 项目相关文档
- 架构说明
- 使用指南

### `/legacy/` 历史版本目录
- Day1-Day7的历史开发版本
- 用于参考和版本追溯
- 不影响当前生产系统

### `/logs/` 日志文件目录
- 系统运行日志
- 详细的测试结果日志
- 便于问题排查和分析

### `/tests/` 测试套件目录
- 完整的自动化测试系统
- 确保功能完整性
- 支持回归测试

### `/checkpoints/` 断点目录
- Day8新增的断点恢复功能
- 自动保存处理进度
- 支持中断后恢复

## 🔄 版本演进

- **Day1-Day2**: Jupyter Notebook原型开发
- **Day3-Day5**: Python脚本化，功能逐步完善
- **Day6**: 数据集分类管理
- **Day7**: 组合策略配置系统
- **Day8**: 断点恢复+智能进度监控（当前生产版本）

## 🚀 快速开始

### 运行Day8系统
```bash
cd /Users/<USER>/Documents/Alpha/alpha
python3 day8.py
```

### 运行测试
```bash
cd tests
python3 run_all_tests.py
```

### 查看日志
```bash
tail -f logs/simulation.log
tail -f logs/alpha_test_results.log
```

## 📊 文件大小统计

- **配置文件**: ~40KB
- **数据文件**: ~4KB
- **日志文件**: ~6MB
- **代码文件**: ~80KB（Day8）+ 历史版本
- **测试文件**: ~75KB

---

*最后更新: 2025-09-10*  
*结构版本: v1.0*  
*当前生产版本: Day8*