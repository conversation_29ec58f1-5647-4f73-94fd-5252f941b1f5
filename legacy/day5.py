# WorldQuant Brain Alpha策略智能批量生成系统 (Day5)
# 基于day4.py的完整功能，扩展支持14个数据集和完整运算符库
# 主要改进：配置文件驱动、数据集管理器、运算符库管理器

import requests
import json
import os
import logging
from datetime import datetime
from os.path import expanduser
from time import sleep
from requests.auth import HTTPBasicAuth

# ==================== 配置常量 ====================

# 文件路径配置
QUALIFIED_ALPHA_FILE = 'qualified_alphas.txt'  # 合格Alpha ID存储文件
SIMULATION_LOG_FILE = 'simulation.log'         # 日志文件
CREDENTIALS_FILE = 'brain.txt'                 # 认证凭据文件
DATASETS_CONFIG_FILE = 'datasets_config.json'  # 数据集配置文件
OPERATORS_CONFIG_FILE = 'operators_config.json' # 运算符配置文件

# ==================== 配置加载器 ====================

def load_datasets_config():
    """加载数据集配置"""
    try:
        with open(DATASETS_CONFIG_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 找不到数据集配置文件: {DATASETS_CONFIG_FILE}")
        return None
    except json.JSONDecodeError:
        print(f"❌ 数据集配置文件格式错误: {DATASETS_CONFIG_FILE}")
        return None

def load_operators_config():
    """加载运算符配置"""
    try:
        with open(OPERATORS_CONFIG_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 找不到运算符配置文件: {OPERATORS_CONFIG_FILE}")
        return None
    except json.JSONDecodeError:
        print(f"❌ 运算符配置文件格式错误: {OPERATORS_CONFIG_FILE}")
        return None

# ==================== 数据集管理器类 ====================

class DatasetManager:
    """数据集管理器 - 统一管理14个数据集的配置和选择"""
    
    def __init__(self):
        self.config = load_datasets_config()
        if not self.config:
            raise Exception("无法加载数据集配置")
        self.datasets = self.config['datasets']
        self.default_config = self.config['default_config']
    
    def list_all_datasets(self):
        """列出所有可用数据集"""
        print("\n📊 可用数据集列表 (共14个):")
        print("="*80)
        
        for i, (dataset_id, config) in enumerate(self.datasets.items(), 1):
            print(f"{i:2d}. {dataset_id:<15} - {config['name']}")
            print(f"    类别: {config['category']}")
            print(f"    覆盖率: {config['coverage']:<4} | 字段数: {config['field_count']:<3} | 描述: {config['description'][:60]}...")
            print()
    
    def get_dataset_by_choice(self, choice):
        """根据用户选择获取数据集配置"""
        try:
            choice_num = int(choice)
            dataset_ids = list(self.datasets.keys())
            
            if 1 <= choice_num <= len(dataset_ids):
                dataset_id = dataset_ids[choice_num - 1]
                config = self.datasets[dataset_id]
                
                # 选择最佳配置
                best_universe = self._select_best_universe(config['universes'])
                best_delay = str(config['delays'][0])  # 优先选择最小延迟
                
                search_scope = {
                    'instrumentType': self.default_config['instrumentType'],
                    'region': config['regions'][0],
                    'delay': best_delay,
                    'universe': best_universe
                }
                
                return dataset_id, search_scope, config
            else:
                return None, None, None
                
        except ValueError:
            return None, None, None
    
    def _select_best_universe(self, universes):
        """选择最佳股票池（优先选择覆盖面大的）"""
        priority = self.default_config['universe_priority']
        for universe in priority:
            if universe in universes:
                return universe
        return universes[0] if universes else 'TOP1000'
    
    def get_dataset_info(self, dataset_id):
        """获取数据集详细信息"""
        return self.datasets.get(dataset_id, None)

# ==================== 运算符库管理器类 ====================

class OperatorLibrary:
    """运算符库管理器 - 统一管理所有运算符的分类和使用"""
    
    def __init__(self):
        self.config = load_operators_config()
        if not self.config:
            raise Exception("无法加载运算符配置")
        self.operators = self.config['operator_categories']
        self.operator_sets = self.config['operator_sets']
    
    def get_operator_set(self, set_name='basic'):
        """获取运算符组合"""
        return self.operator_sets.get(set_name, self.operator_sets['basic'])

# ==================== 继承Day4的所有核心功能 ====================

def sign_in():
    """
    WorldQuant Brain API认证函数（继承自day4.py）
    """
    try:
        with open(expanduser(CREDENTIALS_FILE)) as f:
            credentials = json.load(f)
        
        username, password = credentials
        sess = requests.Session()
        sess.auth = HTTPBasicAuth(username, password)
        
        response = sess.post('https://api.worldquantbrain.com/authentication')
        
        if response.status_code == 201:
            print("✅ WorldQuant Brain API认证成功")
        else:
            print(f"❌ 认证失败，状态码: {response.status_code}")
        
        return sess
        
    except FileNotFoundError:
        print("❌ 错误：找不到brain.txt凭据文件")
        print("请确保在项目根目录下创建brain.txt文件，格式为: [\"username\", \"password\"]")
        raise
    except json.JSONDecodeError:
        print("❌ 错误：brain.txt文件格式不正确")
        print("正确格式应为: [\"<EMAIL>\", \"your_password\"]")
        raise
    except Exception as e:
        print(f"❌ 认证过程发生错误: {str(e)}")
        raise

def get_datafields(s, searchScope, dataset_id='', search=''):
    """
    获取WorldQuant Brain数据字段信息（继承自day4.py）
    """
    import pandas as pd
    
    instrument_type = searchScope['instrumentType']
    region = searchScope['region']
    delay = searchScope['delay']
    universe = searchScope['universe']
    
    print(f"🔍 开始获取数据字段...")
    print(f"   数据集ID: {dataset_id if dataset_id else '全部'}")
    print(f"   市场范围: {region} {universe}")
    
    if len(search) == 0:
        url_template = "https://api.worldquantbrain.com/data-fields?" + \
                       f"&instrumentType={instrument_type}" + \
                       f"&region={region}&delay={str(delay)}&universe={universe}&dataset.id={dataset_id}&limit=50" + \
                       "&offset={x}"
        
        initial_response = s.get(url_template.format(x=0))
        if initial_response.status_code == 200:
            count = initial_response.json()['count']
            print(f"   总记录数: {count}")
        else:
            print(f"❌ 获取记录数失败，状态码: {initial_response.status_code}")
            count = 0
    else:
        url_template = "https://api.worldquantbrain.com/data-fields?" + \
                       f"&instrumentType={instrument_type}" + \
                       f"&region={region}&delay={str(delay)}&universe={universe}&limit=50" + \
                       f"&search={search}" + \
                       "&offset={x}"
        count = 100
    
    datafields_list = []
    pages_processed = 0
    
    for x in range(0, count, 50):
        try:
            datafields = s.get(url_template.format(x=x))
            if datafields.status_code == 200:
                results = datafields.json()['results']
                datafields_list.append(results)
                pages_processed += 1
                print(f"   已处理第 {pages_processed} 页，获取 {len(results)} 条记录")
            else:
                print(f"❌ 第 {pages_processed + 1} 页请求失败，状态码: {datafields.status_code}")
                break
        except Exception as e:
            print(f"❌ 处理第 {pages_processed + 1} 页时发生错误: {e}")
            break
    
    datafields_list_flat = [item for sublist in datafields_list for item in sublist]
    datafields_df = pd.DataFrame(datafields_list_flat)
    
    print(f"✅ 数据字段获取完成，共 {len(datafields_df)} 条记录")
    return datafields_df

def check_alpha_quality(alpha_data):
    """
    检查Alpha是否符合7个核心质量标准（继承自day4.py）
    """
    is_data = alpha_data.get('is', {})
    
    # 提取关键指标（严格按照API返回的字段名）
    sharpe = is_data.get('sharpe', 0)
    fitness = is_data.get('fitness', 0)
    turnover = is_data.get('turnover', 0) * 100  # 转换为百分比
    sub_universe_sharpe = is_data.get('subUniverseSharpe', 0)  # 注意字段名
    weight_concentration = is_data.get('maxWeight', 0) * 100  # 最大权重作为权重集中度
    ic_mean = is_data.get('ic_mean', 0)
    returns = is_data.get('returns', 0)
    
    # 7个核心质量标准检查（严格按照权威案例）
    checks = {
        # PASS标准 (4个)
        'turnover_above_min': {
            'passed': turnover >= 1.0,
            'message': f"Turnover of {turnover:.2f}% is {'above' if turnover >= 1.0 else 'below'} cutoff of 1%.",
            'status': 'PASS' if turnover >= 1.0 else 'FAIL'
        },
        'turnover_below_max': {
            'passed': turnover <= 70.0,
            'message': f"Turnover of {turnover:.2f}% is {'below' if turnover <= 70.0 else 'above'} cutoff of 70%.",
            'status': 'PASS' if turnover <= 70.0 else 'FAIL'
        },
        'sub_universe_sharpe_ok': {
            'passed': sub_universe_sharpe >= -1.54,
            'message': f"Sub-universe Sharpe of {sub_universe_sharpe:.2f} is {'above' if sub_universe_sharpe >= -1.54 else 'below'} cutoff of -1.54.",
            'status': 'PASS' if sub_universe_sharpe >= -1.54 else 'FAIL'
        },
        'competition_challenge': {
            'passed': True,  # 默认通过竞赛挑战
            'message': "Competition Challenge matches.",
            'status': 'PASS'
        },
        
        # FAIL标准 (3个) - 需要全部通过才算合格
        'sharpe_ok': {
            'passed': sharpe >= 1.25,
            'message': f"Sharpe of {sharpe:.2f} is {'above' if sharpe >= 1.25 else 'below'} cutoff of 1.25.",
            'status': 'PASS' if sharpe >= 1.25 else 'FAIL'
        },
        'fitness_ok': {
            'passed': fitness >= 1.0,
            'message': f"Fitness of {fitness:.2f} is {'above' if fitness >= 1.0 else 'below'} cutoff of 1.",
            'status': 'PASS' if fitness >= 1.0 else 'FAIL'
        },
        'weight_concentration_ok': {
            'passed': weight_concentration <= 10.0,
            'message': f"Weight concentration {weight_concentration:.0f}% is {'above' if weight_concentration <= 10.0 else 'below'} cutoff of 10% on {datetime.now().strftime('%m/%d/%Y')}.",
            'status': 'PASS' if weight_concentration <= 10.0 else 'FAIL'
        }
    }
    
    # 计算通过的检查项数量
    passed_checks = sum(1 for check in checks.values() if check['passed'])
    total_checks = len(checks)
    
    # 需要全部7个检查项都通过才算合格（严格标准）
    is_qualified = passed_checks == 7
    
    result = {
        'qualified': is_qualified,
        'passed_checks': passed_checks,
        'total_checks': total_checks,
        'metrics': {
            'sharpe': sharpe,
            'fitness': fitness,
            'turnover': turnover,
            'sub_universe_sharpe': sub_universe_sharpe,
            'weight_concentration': weight_concentration,
            'ic_mean': ic_mean,
            'returns': returns
        },
        'checks': checks
    }
    
    return is_qualified, result

def save_qualified_alpha(alpha_id, alpha_expression, quality_result, alpha_index=None):
    """
    保存合格的Alpha ID到文件（继承自day4.py）
    """
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 准备保存的数据（包含完整的检测结果和序号）
    alpha_record = {
        'alpha_id': alpha_id,
        'expression': alpha_expression,
        'alpha_index': alpha_index,  # 添加序号
        'timestamp': timestamp,
        'qualified': quality_result['qualified'],
        'passed_checks': quality_result['passed_checks'],
        'total_checks': quality_result['total_checks'],
        'metrics': quality_result['metrics'],
        'detailed_checks': quality_result['checks']
    }
    
    # 追加到合格Alpha文件
    with open(QUALIFIED_ALPHA_FILE, 'a', encoding='utf-8') as f:
        f.write(json.dumps(alpha_record, ensure_ascii=False) + '\n')
    
    # 同时保存到专门的成功Alpha ID文件（包含序号）
    success_alpha_file = 'successful_alpha_ids.txt'
    
    # 如果文件不存在，先写入表头
    if not os.path.exists(success_alpha_file):
        with open(success_alpha_file, 'w', encoding='utf-8') as f:
            f.write("Alpha_ID\t序号\tAlpha表达式\t时间戳\t通过标准\n")
    
    with open(success_alpha_file, 'a', encoding='utf-8') as f:
        index_str = f"第{alpha_index}个" if alpha_index else "未知序号"
        f.write(f"{alpha_id}\t{index_str}\t{alpha_expression}\t{timestamp}\t{quality_result['passed_checks']}/{quality_result['total_checks']}\n")
    
    print(f"💾 已保存合格Alpha: {alpha_id} ({index_str})")
    print(f"   详细记录: {QUALIFIED_ALPHA_FILE}")
    print(f"   ID列表: {success_alpha_file}")
    
    # 特别提醒：这是完全通过7个标准的Alpha
    if quality_result['passed_checks'] == 7:
        print(f"🏆 重要发现！Alpha {alpha_id} 完全通过所有7个标准！")
        print(f"   这是第 {alpha_index} 个测试的Alpha，请重点关注！")

def display_alpha_result(alpha_id, alpha_expression, quality_result):
    """
    显示Alpha测试结果（继承自day4.py）
    """
    print(f"\n" + "="*60)
    print(f"📊 Alpha测试结果 - ID: {alpha_id}")
    print(f"="*60)
    print(f"表达式: {alpha_expression}")
    print(f"质量评级: {'✅ 合格' if quality_result['qualified'] else '❌ 不合格'}")
    print(f"通过检查: {quality_result['passed_checks']}/{quality_result['total_checks']}")
    
    print(f"\n📋 IS Testing Status (IS 测试状态)")
    
    # 统计PASS和FAIL数量
    pass_count = sum(1 for check in quality_result['checks'].values() if check['status'] == 'PASS')
    fail_count = sum(1 for check in quality_result['checks'].values() if check['status'] == 'FAIL')
    
    print(f"\n✅ {pass_count} PASS ({pass_count} 及格)")
    for check_name, check_info in quality_result['checks'].items():
        if check_info['status'] == 'PASS':
            print(f"   {check_info['message']}")
    
    if fail_count > 0:
        print(f"\n❌ {fail_count} FAIL ({fail_count} 不及格)")
        for check_name, check_info in quality_result['checks'].items():
            if check_info['status'] == 'FAIL':
                print(f"   {check_info['message']}")
    
    print(f"\n📈 详细性能指标:")
    metrics = quality_result['metrics']
    print(f"   Sharpe: {metrics['sharpe']:.2f}")
    print(f"   Fitness: {metrics['fitness']:.2f}")
    print(f"   Turnover: {metrics['turnover']:.2f}%")
    print(f"   Sub-universe Sharpe: {metrics['sub_universe_sharpe']:.2f}")
    print(f"   Weight Concentration: {metrics['weight_concentration']:.0f}%")
    print(f"   IC Mean: {metrics['ic_mean']:.4f}")
    print(f"   Returns: {metrics['returns']:.4f}")
    print(f"\n" + "="*60)

def view_qualified_alphas():
    """
    查看已保存的合格Alpha策略（继承自day4.py）
    """
    if not os.path.exists(QUALIFIED_ALPHA_FILE):
        print("📝 暂无保存的合格Alpha策略")
        return
    
    print("\n📋 已保存的合格Alpha策略:")
    print("="*60)
    
    try:
        with open(QUALIFIED_ALPHA_FILE, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if not lines:
            print("📝 暂无保存的合格Alpha策略")
            return
        
        for i, line in enumerate(lines, 1):
            try:
                alpha_record = json.loads(line.strip())
                print(f"\n{i}. Alpha ID: {alpha_record['alpha_id']}")
                print(f"   表达式: {alpha_record['expression']}")
                print(f"   保存时间: {alpha_record['timestamp']}")
                print(f"   通过检查: {alpha_record['passed_checks']}/{alpha_record['total_checks']}")
                
                metrics = alpha_record['metrics']
                print(f"   夏普比率: {metrics['sharpe']:.3f} | 适应度: {metrics['fitness']:.3f}")
                
            except json.JSONDecodeError:
                print(f"   ❌ 第{i}行数据格式错误")
                
    except Exception as e:
        print(f"❌ 读取文件时发生错误: {str(e)}")

def setup_logging():
    """
    配置日志系统（继承自day4.py）
    """
    logging.basicConfig(
        filename=SIMULATION_LOG_FILE,
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        filemode='a'
    )
    
    # 创建一个自定义的日志记录器，用于记录详细的Alpha测试结果
    detailed_logger = logging.getLogger('alpha_details')
    detailed_handler = logging.FileHandler('alpha_test_results.log', mode='a', encoding='utf-8')
    detailed_formatter = logging.Formatter('%(asctime)s - %(message)s')
    detailed_handler.setFormatter(detailed_formatter)
    detailed_logger.addHandler(detailed_handler)
    detailed_logger.setLevel(logging.INFO)
    
    return detailed_logger

def log_alpha_result(alpha_id, alpha_expression, quality_result, detailed_logger):
    """
    记录Alpha测试结果到详细日志文件（继承自day4.py）
    """
    log_content = f"""
{'='*60}
Alpha测试结果 - ID: {alpha_id}
{'='*60}
表达式: {alpha_expression}
质量评级: {'合格' if quality_result['qualified'] else '不合格'}
通过检查: {quality_result['passed_checks']}/{quality_result['total_checks']}

IS Testing Status (IS 测试状态)
"""
    
    # 统计PASS和FAIL数量
    pass_count = sum(1 for check in quality_result['checks'].values() if check['status'] == 'PASS')
    fail_count = sum(1 for check in quality_result['checks'].values() if check['status'] == 'FAIL')
    
    log_content += f"\n{pass_count} PASS ({pass_count} 及格)\n"
    for check_name, check_info in quality_result['checks'].items():
        if check_info['status'] == 'PASS':
            log_content += f"   {check_info['message']}\n"
    
    if fail_count > 0:
        log_content += f"\n{fail_count} FAIL ({fail_count} 不及格)\n"
        for check_name, check_info in quality_result['checks'].items():
            if check_info['status'] == 'FAIL':
                log_content += f"   {check_info['message']}\n"
    
    log_content += f"\n详细性能指标:\n"
    metrics = quality_result['metrics']
    log_content += f"   Sharpe: {metrics['sharpe']:.2f}\n"
    log_content += f"   Fitness: {metrics['fitness']:.2f}\n"
    log_content += f"   Turnover: {metrics['turnover']:.2f}%\n"
    log_content += f"   Sub-universe Sharpe: {metrics['sub_universe_sharpe']:.2f}\n"
    log_content += f"   Weight Concentration: {metrics['weight_concentration']:.0f}%\n"
    log_content += f"   IC Mean: {metrics['ic_mean']:.4f}\n"
    log_content += f"   Returns: {metrics['returns']:.4f}\n"
    log_content += f"{'='*60}\n"
    
    detailed_logger.info(log_content)

# ==================== Day5新增和增强功能 ====================

def show_main_menu_v5():
    """
    显示Day5主菜单（与day4保持一致的3个模式）
    """
    print("\n" + "="*60)
    print("🚀 WorldQuant Brain Alpha策略智能生成系统 (Day5)")
    print("="*60)
    print("\n📋 请选择运行模式:")
    print("1. 自动模式 - 生成、测试并提交Alpha策略")
    print("2. 提交模式 - 查看已保存的合格Alpha策略")
    print("3. 退出系统")

def generate_alpha_expressions_v5(datafields_list, operator_set='basic'):
    """
    Day5增强版Alpha表达式生成器
    
    参数说明：
    - datafields_list: 数据字段列表
    - operator_set: 运算符集合类型 ('basic' 或 'advanced')
    """
    op_lib = OperatorLibrary()
    ops = op_lib.get_operator_set(operator_set)
    
    print(f"\n🔧 配置Alpha策略构建参数 ({operator_set}模式)...")
    print(f"   分组操作符: {len(ops['group_ops'])} 种 - {ops['group_ops']}")
    print(f"   时间序列操作符: {len(ops['ts_ops'])} 种 - {ops['ts_ops']}")
    print(f"   基本面字段: {len(datafields_list)} 个")
    print(f"   时间周期: {ops['periods']} 天")
    print(f"   分组方法: {len(ops['groups'])} 种 - {ops['groups']}")
    
    alpha_expressions = []
    expression_count = 0
    
    total_combinations = len(ops['group_ops']) * len(ops['ts_ops']) * len(datafields_list) * len(ops['periods']) * len(ops['groups'])
    print(f"   预期生成表达式总数: {total_combinations:,} 个")
    
    for gco in ops['group_ops']:
        for tco in ops['ts_ops']:
            for cf in datafields_list:
                for d in ops['periods']:
                    for grp in ops['groups']:
                        alpha_expression = f"{gco}({tco}({cf}, {d}), {grp})"
                        alpha_expressions.append(alpha_expression)
                        expression_count += 1
                        
                        if expression_count % 1000 == 0:
                            print(f"   已生成 {expression_count:,} 个表达式...")
    
    print(f"\n✅ Alpha表达式生成完成！")
    print(f"   实际生成表达式总数: {len(alpha_expressions):,} 个")
    print(f"   表达式示例:")
    for i, expr in enumerate(alpha_expressions[:5], 1):
        print(f"     {i}. {expr}")
    
    return alpha_expressions

def create_alpha_list(alpha_expressions, searchScope):
    """
    将Alpha表达式封装为WorldQuant Brain模拟请求格式（继承自day4.py）
    """
    print("\n🎯 开始Alpha策略封装和模拟准备阶段")
    print("="*60)
    print("\n📦 将Alpha表达式封装为WorldQuant Brain模拟请求格式...")
    
    alpha_list = []
    
    for index, alpha_expression in enumerate(alpha_expressions, start=1):
        if index <= 5:  # 只显示前5个的详细信息
            print(f"📝 正在处理第 {index} 个Alpha表达式...")
            print(f"   表达式: {alpha_expression}")
        
        simulation_data = {
            "type": "REGULAR",
            "settings": {
                "instrumentType": searchScope['instrumentType'],
                "region": searchScope['region'],
                "universe": searchScope['universe'],
                "delay": int(searchScope['delay']),
                "decay": 0,
                "neutralization": "SUBINDUSTRY",
                "truncation": 0.01,
                "pasteurization": "ON",
                "unitHandling": "VERIFY",
                "nanHandling": "OFF",
                "language": "FASTEXPR",
                "visualization": False,
            },
            "regular": alpha_expression
        }
        
        alpha_list.append(simulation_data)
        
        if index % 100 == 0 or index <= 5:
            print(f"   ✅ 已封装 {len(alpha_list)} 个Alpha策略")
    
    print(f"\n📊 Alpha策略封装完成统计：")
    print(f"   总策略数量: {len(alpha_list):,} 个")
    print(f"   封装格式: WorldQuant Brain标准模拟请求")
    print(f"   目标市场: {searchScope['region']} {searchScope['universe']}")
    print(f"   中性化方法: 子行业中性")
    
    if alpha_list:
        print(f"\n🔍 封装示例 (第1个Alpha策略):")
        print(f"   Alpha表达式: {alpha_list[0]['regular']}")
    
    return alpha_list

def batch_submit_alphas(sess, alpha_list, test_mode=True):
    """
    批量提交Alpha策略（完全继承自day4.py的实现）
    
    参数说明：
    - sess: 已认证的会话对象
    - alpha_list: Alpha策略列表
    - test_mode: 是否为测试模式（True=只处理前5个，False=处理全部）
    
    返回值：
    - tuple: (成功数量, 失败数量, 合格Alpha列表)
    """
    print("\n🚀 开始Alpha策略批量提交和模拟执行")
    print("="*60)
    print("\n⚙️ 模拟执行配置：")
    print(f"   - 处理策略数量: 全部 {len(alpha_list):,} 个Alpha策略")
    print("   - 最大重试次数: 15次")
    print("   - 自动重连机制: 启用")
    print("   - 错误处理: 全面覆盖")
    print("   - 日志记录: 启用")
    
    # Alpha提交失败容忍度配置
    alpha_fail_attempt_tolerance = 15  # 每个Alpha允许的最大失败尝试次数
    
    # 初始化提交统计计数器
    total_processed = 0      # 总处理数量
    successful_submissions = 0  # 成功提交数量
    failed_submissions = 0   # 失败提交数量
    qualified_alphas = []    # 合格的Alpha列表
    
    print(f"\n🎯 开始批量提交Alpha策略 (处理全部 {len(alpha_list):,} 个策略)")
    print(f"   失败容忍度: {alpha_fail_attempt_tolerance} 次重试")
    
    # 根据模式决定处理数量
    if test_mode:
        max_test_count = min(5, len(alpha_list))  # 测试模式：最多测试5个Alpha
        print(f"\n🧪 测试模式：只处理前 {max_test_count} 个Alpha进行质量检测")
        alpha_subset = alpha_list[:max_test_count]
    else:
        max_test_count = len(alpha_list)  # 生产模式：处理全部Alpha
        print(f"\n🚀 生产模式：处理全部 {max_test_count} 个Alpha进行质量检测")
        alpha_subset = alpha_list
    
    for alpha_index, alpha in enumerate(alpha_subset, start=1):
        total_processed += 1
        
        print(f"\n" + "-"*50)
        print(f"📤 正在处理第 {alpha_index} 个Alpha策略")
        print(f"   Alpha表达式: {alpha['regular']}")
        
        # 记录开始处理的日志
        logging.info(f"开始处理Alpha {alpha_index}: {alpha['regular']}")
        
        # ==================== 智能重试机制 ====================
        
        keep_trying = True       # 控制while循环继续的标志
        failure_count = 0        # 记录当前Alpha的失败尝试次数
        submission_successful = False  # 提交成功标志
        alpha_id = None
        
        # 实施智能重试循环：处理网络问题、认证失效、服务器错误等情况
        while keep_trying:
            try:
                print(f"\n🔄 尝试提交 (第 {failure_count + 1} 次)...")
                
                # 向WorldQuant Brain API发送Alpha模拟请求
                # 这是核心的API调用，将Alpha策略提交给服务器进行回测
                sim_resp = sess.post(
                    'https://api.worldquantbrain.com/simulations',
                    json=alpha,  # 将当前Alpha策略（JSON格式）发送到服务器
                    timeout=30   # 设置30秒超时，避免长时间等待
                )
                
                # ==================== 成功响应处理 ====================
                
                # 检查响应状态码，201表示成功创建模拟
                if sim_resp.status_code == 201:
                    # 从响应头中获取模拟进度跟踪URL
                    # Location头包含了模拟任务的唯一标识符和跟踪链接
                    sim_progress_url = sim_resp.headers.get('Location', 'URL not found')
                    
                    # 记录成功信息到日志和控制台
                    success_message = f'✅ Alpha {alpha_index} 提交成功！Location: {sim_progress_url}'
                    logging.info(success_message)
                    print(success_message)
                    
                    # 显示详细的响应信息
                    print(f"   状态码: {sim_resp.status_code}")
                    
                    # 按照YHYYDS666项目的逻辑等待模拟完成并获取Alpha ID
                    try:
                        start_time = datetime.now()
                        total_wait = 0
                        alpha_id = None
                        
                        print(f"   ⏳ 等待模拟完成...")
                        
                        while True:
                            sim_progress_resp = sess.get(sim_progress_url)
                            retry_after_sec = float(sim_progress_resp.headers.get("Retry-After", 0))
                            
                            if retry_after_sec == 0:  # simulation done!
                                # 检查响应状态码
                                if sim_progress_resp.status_code == 200:
                                    response_data = sim_progress_resp.json()
                                    print(f"   📋 模拟完成响应: {response_data}")
                                    
                                    # 尝试从不同字段获取Alpha ID
                                    alpha_id = None
                                    if 'alpha' in response_data:
                                        alpha_id = response_data['alpha']
                                    elif 'alphaId' in response_data:
                                        alpha_id = response_data['alphaId']
                                    elif 'id' in response_data:
                                        alpha_id = response_data['id']
                                    else:
                                        # 从URL中提取ID作为备用方案
                                        alpha_id = sim_progress_url.split('/')[-1] if '/' in sim_progress_url else 'Unknown'
                                        print(f"   ⚠️ 无法从响应中找到Alpha ID，使用URL ID: {alpha_id}")
                                    
                                    print(f"   ✅ 获得 Alpha ID: {alpha_id}")
                                    break
                                else:
                                    print(f"   ❌ 模拟完成但状态码异常: {sim_progress_resp.status_code}")
                                    alpha_id = sim_progress_url.split('/')[-1] if '/' in sim_progress_url else 'Unknown'
                                    break
                            
                            # 更新等待时间和进度
                            total_wait += retry_after_sec
                            elapsed = (datetime.now() - start_time).total_seconds()
                            progress = min(95, (elapsed / 30) * 100)  # 假设通常需要 30 秒完成
                            print(f"   ⏳ 等待模拟结果... ({elapsed:.1f} 秒 | 进度约 {progress:.0f}%)")
                            sleep(retry_after_sec)
                            
                            # 防止无限等待
                            if elapsed > 120:  # 最多等待2分钟
                                print(f"   ⚠️ 模拟超时，但Alpha已提交")
                                alpha_id = sim_progress_url.split('/')[-1] if '/' in sim_progress_url else 'Unknown'
                                break
                        
                        # 更新统计计数器
                        successful_submissions += 1
                        submission_successful = True
                        keep_trying = False  # 成功提交，退出重试循环
                        
                    except Exception as e:
                        print(f"   ⚠️ 获取Alpha ID时出错: {str(e)}，但Alpha已成功提交")
                        alpha_id = sim_progress_url.split('/')[-1] if '/' in sim_progress_url else 'Unknown'
                        successful_submissions += 1
                        submission_successful = True
                        keep_trying = False
                    
                else:
                    # ==================== 失败响应处理 ====================
                    
                    error_message = f"❌ Alpha {alpha_index} 提交失败，状态码: {sim_resp.status_code}"
                    print(error_message)
                    print(f"   响应内容: {sim_resp.text[:200]}...")  # 显示前200字符
                    logging.error(f"{error_message}, 响应: {sim_resp.text}")
                    
                    # 根据不同的HTTP状态码采取相应措施
                    if sim_resp.status_code in [401, 403]:
                        # 认证问题：重新登录
                        print("🔐 检测到认证问题，尝试重新登录...")
                        sess = sign_in()
                    elif sim_resp.status_code >= 500:
                        # 服务器错误：通常是临时性问题
                        print("🔧 服务器错误，这通常是临时性问题")
                    
                    failure_count += 1
                    
            except Exception as e:
                # ==================== 通用异常处理 ====================
                
                # 处理所有其他未预期的异常
                general_error = f"🚨 Alpha {alpha_index} 发生未预期异常: {type(e).__name__}: {str(e)}"
                logging.error(general_error)
                print(general_error)
                print("   尝试重新建立会话...")
                
                failure_count += 1
            
            # ==================== 重试逻辑和失败处理 ====================
            
            # 检查是否达到失败容忍上限
            if failure_count >= alpha_fail_attempt_tolerance:
                # 达到最大重试次数，放弃当前Alpha
                final_error = f"💔 Alpha {alpha_index} 达到最大重试次数 ({alpha_fail_attempt_tolerance})，放弃处理"
                logging.error(final_error)
                print(final_error)
                
                # 尝试重新登录会话，为下一个Alpha做准备
                print("   尝试重新登录以处理下一个Alpha...")
                sess = sign_in()
                
                # 重置失败计数器，继续处理下一个Alpha
                failure_count = 0
                failed_submissions += 1
                keep_trying = False  # 退出while循环，移动到下一个Alpha
                
                break  # 退出重试循环
            
            # 如果需要重试且未达到上限，添加延迟避免过于频繁的请求
            if keep_trying and not submission_successful:
                # 实施指数退避策略：重试间隔逐渐增加
                wait_time = min(2 ** (failure_count - 1), 15)  # 最大等待15秒
                print(f"   等待 {wait_time} 秒后进行第 {failure_count + 1} 次重试...")
                sleep(wait_time)
        
        # ==================== 单个Alpha处理完成 ====================
        
        completion_message = f"📊 Alpha {alpha_index} 处理完成"
        if submission_successful:
            completion_message += " - ✅ 成功"
            print(completion_message)
            logging.info(completion_message)
            
            # 立即进行质量检测
            print(f"\n🔍 开始对Alpha {alpha_id} 进行7个标准质量检测...")
            
            try:
                # 等待一下让指标计算完成
                sleep(5)
                
                alpha_url = f"https://api.worldquantbrain.com/alphas/{alpha_id}"
                alpha_detail = sess.get(alpha_url)
                
                if alpha_detail.status_code == 200:
                    alpha_data = alpha_detail.json()
                    
                    # 检查是否有 is 字段
                    if 'is' not in alpha_data:
                        print(f"❌ Alpha {alpha_id} 无法获取指标数据，可能还在计算中")
                        print(f"   建议稍后手动检查Alpha ID: {alpha_id}")
                    else:
                        print(f"✅ 获取到Alpha {alpha_id} 的性能指标")
                        
                        # 进行7个标准的质量检测
                        is_qualified, quality_result = check_alpha_quality(alpha_data)
                        
                        # 显示测试结果
                        display_alpha_result(alpha_id, alpha['regular'], quality_result)
                        
                        # 记录到详细日志
                        detailed_logger = logging.getLogger('alpha_details')
                        log_alpha_result(alpha_id, alpha['regular'], quality_result, detailed_logger)
                        
                        # 如果合格，保存到文件
                        if is_qualified:
                            save_qualified_alpha(alpha_id, alpha['regular'], quality_result, alpha_index)
                            print(f"🎉 Alpha {alpha_id} 通过质量检测并已保存！")
                            qualified_alphas.append({
                                'alpha_id': alpha_id,
                                'expression': alpha['regular'],
                                'index': alpha_index,
                                'qualified': True
                            })
                        else:
                            print(f"❌ Alpha {alpha_id} 未通过质量检测")
                            qualified_alphas.append({
                                'alpha_id': alpha_id,
                                'expression': alpha['regular'],
                                'index': alpha_index,
                                'qualified': False
                            })
                        
                        # 记录到基础日志
                        logging.info(f"Alpha {alpha_id}: {'合格' if is_qualified else '不合格'} - {quality_result['passed_checks']}/{quality_result['total_checks']} 通过")
                        
                else:
                    print(f"❌ 无法获取Alpha {alpha_id} 的详细结果，状态码: {alpha_detail.status_code}")
                    
            except Exception as e:
                print(f"❌ 检测Alpha {alpha_id} 时发生错误: {str(e)}")
                logging.error(f"检测Alpha {alpha_id} 时发生错误: {str(e)}")
        else:
            completion_message += " - ❌ 失败"
            print(completion_message)
            logging.info(completion_message)
        
        # 显示当前处理进度
        print(f"\n📊 当前进度: {total_processed}/{max_test_count}")
        print(f"   成功: {successful_submissions}, 失败: {failed_submissions}")
        if qualified_alphas:
            qualified_count = sum(1 for alpha in qualified_alphas if alpha.get('qualified', False))
            print(f"   合格: {qualified_count}")
    
    # ==================== 批量提交总结报告 ====================
    
    print(f"\n" + "="*60)
    print(f"🏁 Alpha策略批量提交完成")
    print(f"="*60)
    
    # 生成详细的统计报告
    print(f"\n📈 提交统计报告：")
    print(f"   总处理数量: {total_processed} 个Alpha策略")
    print(f"   成功提交: {successful_submissions} 个 ({successful_submissions/total_processed*100:.1f}%)")
    print(f"   提交失败: {failed_submissions} 个 ({failed_submissions/total_processed*100:.1f}%)")
    
    if total_processed > 0:
        success_rate = successful_submissions / total_processed * 100
        print(f"   整体成功率: {success_rate:.1f}%")
    
    # 记录最终统计到日志
    final_log = f"批量提交完成 - 总数:{total_processed}, 成功:{successful_submissions}, 失败:{failed_submissions}"
    logging.info(final_log)
    
    return successful_submissions, failed_submissions, qualified_alphas

def main():
    """
    Day5主程序入口
    """
    try:
        # 初始化管理器
        dataset_manager = DatasetManager()
        operator_library = OperatorLibrary()
        
        # 配置日志（继承day4功能）
        detailed_logger = setup_logging()
        
        print("🔐 正在进行API认证...")
        sess = sign_in()
        
        while True:
            show_main_menu_v5()
            
            try:
                choice = input("\n请选择模式 (1-3): ").strip()
                
                if choice == '3':
                    print("👋 感谢使用Day5系统，再见！")
                    break
                    
                elif choice == '2':
                    # 提交模式（继承day4功能）
                    view_qualified_alphas()
                    input("\n按回车键继续...")
                    continue
                    
                elif choice == '1':
                    # 自动模式（增强版）
                    dataset_manager.list_all_datasets()
                    dataset_choice = input("\n请选择数据集 (1-14): ").strip()
                    
                    dataset_id, search_scope, config = dataset_manager.get_dataset_by_choice(dataset_choice)
                    
                    if not dataset_id:
                        print("❌ 无效的数据集选择")
                        continue
                    
                    print(f"\n✅ 已选择数据集: {config['name']}")
                    print(f"   配置: {search_scope}")
                    
                    # 选择运算符复杂度
                    print("\n🔧 请选择运算符复杂度:")
                    print("1. 基础模式 - 3种分组运算符 × 3种时间序列运算符")
                    print("2. 高级模式 - 6种分组运算符 × 6种时间序列运算符")
                    
                    complexity_choice = input("请选择复杂度 (1-2): ").strip()
                    operator_set = 'advanced' if complexity_choice == '2' else 'basic'
                    
                    print(f"\n🎯 开始Alpha策略数据准备阶段")
                    print(f"="*60)
                    
                    # 获取数据字段
                    datafields_df = get_datafields(sess, search_scope, dataset_id)
                    
                    if datafields_df.empty:
                        print("❌ 未获取到数据字段，请检查网络连接")
                        continue
                    
                    # 过滤MATRIX类型字段
                    matrix_fields = datafields_df[datafields_df['type'] == "MATRIX"]
                    if matrix_fields.empty:
                        print("❌ 未找到MATRIX类型的数据字段")
                        continue
                    
                    datafields_list = matrix_fields['id'].values
                    print(f"\n✅ 数据字段筛选完成：")
                    print(f"   总MATRIX字段数: {len(datafields_list)} 个")
                    print(f"   示例字段: {list(datafields_list[:5])}")
                    
                    # 生成Alpha表达式（Day5增强版）
                    alpha_expressions = generate_alpha_expressions_v5(datafields_list, operator_set)
                    
                    # 询问用户处理模式
                    print("\n⚙️ 请选择处理模式:")
                    print("1. 测试模式 - 只处理前5个Alpha进行质量检测（推荐）")
                    print("2. 生产模式 - 处理全部Alpha策略（可能需要很长时间）")
                    
                    processing_choice = input("请选择模式 (1-2): ").strip()
                    test_mode = processing_choice != '2'  # 默认测试模式
                    
                    if not test_mode:
                        confirm = input(f"\n⚠️ 您选择了生产模式，将处理全部 {len(alpha_expressions):,} 个Alpha策略。\n   这可能需要数小时时间，确认继续？(y/N): ").strip().lower()
                        if confirm != 'y':
                            print("已取消，回到测试模式")
                            test_mode = True
                    
                    # 封装Alpha策略
                    alpha_list = create_alpha_list(alpha_expressions, search_scope)
                    
                    # 自动模式：批量提交并进行质量检测
                    print("\n🚀 自动模式：开始批量提交并测试Alpha策略")
                    successful, failed, qualified_alphas = batch_submit_alphas(sess, alpha_list, test_mode)
                    
                    # 显示最终统计
                    if qualified_alphas:
                        truly_qualified = sum(1 for alpha in qualified_alphas if alpha.get('qualified', False))
                        
                        print(f"\n" + "="*60)
                        print(f"🏁 Alpha策略质量检测完成")
                        print(f"="*60)
                        print(f"📈 最终统计报告：")
                        print(f"   总提交数量: {successful} 个Alpha策略")
                        print(f"   质量检测数量: {len(qualified_alphas)} 个")
                        print(f"   真正合格数量: {truly_qualified} 个")
                        if len(qualified_alphas) > 0:
                            print(f"   合格率: {truly_qualified/len(qualified_alphas)*100:.1f}%")
                        print(f"\n📁 文件输出：")
                        print(f"   合格Alpha详情: {QUALIFIED_ALPHA_FILE}")
                        print(f"   成功Alpha ID列表: successful_alpha_ids.txt")
                        print(f"   详细测试日志: alpha_test_results.log")
                        print(f"   基础日志: {SIMULATION_LOG_FILE}")
                        
                        # 记录最终统计到日志
                        final_log = f"质量检测完成 - 提交:{successful}, 检测:{len(qualified_alphas)}, 合格:{truly_qualified}"
                        logging.info(final_log)
                    else:
                        print(f"\n❌ 没有成功提交的Alpha策略")
                    
                    input("\n按回车键继续...")
                    
                else:
                    print("❌ 无效选择，请输入1-3之间的数字")
                    
            except KeyboardInterrupt:
                print("\n\n👋 用户中断，退出程序")
                break
            except Exception as e:
                print(f"❌ 程序运行出错: {str(e)}")
                logging.error(f"程序运行出错: {str(e)}")
                input("\n按回车键继续...")
                
    except Exception as e:
        print(f"❌ 程序初始化失败: {str(e)}")
        logging.error(f"程序初始化失败: {str(e)}")

if __name__ == "__main__":
    main()