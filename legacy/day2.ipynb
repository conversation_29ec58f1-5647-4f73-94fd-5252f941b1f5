{"cells": [{"cell_type": "code", "execution_count": 10, "id": "c2266df9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["201\n", "{'user': {'id': 'TZ28097'}, 'token': {'expiry': 14400.0}, 'permissions': ['TUTORIAL']}\n"]}], "source": ["# WorldQuant Brain API 认证设置（数据字段查询版本）\n", "# 导入必要的库\n", "import requests  # HTTP请求库\n", "import json      # JSON数据处理\n", "from os.path import expanduser  # 获取用户主目录\n", "from requests.auth import HTTPBasicAuth  # HTTP基本认证\n", "\n", "# 从brain.txt文件读取凭据\n", "with open(expanduser('brain.txt')) as f:\n", "    credentials = json.load(f)\n", "username, password = credentials\n", "\n", "# 创建HTTP会话对象\n", "sess = requests.Session()\n", "\n", "# 配置会话的认证信息\n", "sess.auth = HTTPBasicAuth(username, password)\n", "\n", "# 发送认证请求到WorldQuant Brain API\n", "response = sess.post('https://api.worldquantbrain.com/authentication')\n", "\n", "# 打印认证结果\n", "print(response.status_code)  # 状态码：201表示成功\n", "print(response.json())       # 用户信息和权限详情"]}, {"cell_type": "code", "execution_count": 2, "id": "487df7b1", "metadata": {}, "outputs": [], "source": ["# 数据字段查询功能模块\n", "import pandas as pd  # 数据处理库\n", "import requests      # HTTP请求库\n", "\n", "def get_datafields(s, searchScope, dataset_id: str = '', search: str = ''):\n", "    \"\"\"\n", "    获取WorldQuant Brain数据字段信息\n", "    \n", "    参数说明：\n", "    s: 已认证的requests会话对象\n", "    searchScope: 搜索范围配置字典\n", "    dataset_id: 数据集ID（如fundamental6）\n", "    search: 搜索关键词（可选）\n", "    \"\"\"\n", "    # 从搜索范围中提取参数\n", "    instrument_type = searchScope['instrumentType']  # 金融工具类型（如EQUITY）\n", "    region = searchScope['region']                  # 地区（如USA）\n", "    delay = searchScope['delay']                    # 数据延迟天数\n", "    universe = searchScope['universe']              # 股票池（如TOP3000）\n", "    \n", "    # 根据是否有搜索关键词构建不同的API URL\n", "    if len(search) == 0:\n", "        # 无搜索关键词：获取指定数据集的所有字段\n", "        url_template = f\"https://api.worldquantbrain.com/data-fields?instrumentType={instrument_type}&region={region}&delay={str(delay)}&universe={universe}&dataset.id={dataset_id}&limit=50&offset={{x}}\"\n", "        count = 100  # 默认获取100条记录\n", "    else:\n", "        # 有搜索关键词：按关键词搜索字段\n", "        url_template = f\"https://api.worldquantbrain.com/data-fields?instrumentType={instrument_type}&region={region}&delay={str(delay)}&universe={universe}&limit=50&search={search}&offset={{x}}\"\n", "        count = 100  # 搜索结果限制\n", "\n", "    # 分页获取数据字段信息\n", "    datafields_list = []\n", "    for x in range(0, count, 50):  # 每页50条记录\n", "        datafields = s.get(url_template.format(x=x))\n", "        if datafields.status_code == 200:\n", "            # 成功获取数据，添加到结果列表\n", "            datafields_list.append(datafields.json()['results'])\n", "        else:\n", "            # 请求失败，打印错误信息\n", "            print(f\"Error fetching data at offset {x}: {datafields.status_code}\")\n", "    \n", "    # 将嵌套列表展平为一维列表\n", "    datafields_list_flat = [item for sublist in datafields_list for item in sublist]\n", "    \n", "    # 转换为pandas DataFrame便于数据分析\n", "    datafields_df = pd.DataFrame(datafields_list_flat)\n", "    \n", "    return datafields_df  # 返回包含数据字段信息的DataFrame"]}, {"cell_type": "code", "execution_count": 3, "id": "188bc9d1", "metadata": {}, "outputs": [], "source": ["# 改进版数据字段查询功能（支持动态计数）\n", "import pandas as pd  # 数据处理库\n", "import requests      # HTTP请求库\n", "\n", "def get_datafields(s, searchScope, dataset_id: str = '', search: str = ''):\n", "    \"\"\"\n", "    改进版：获取WorldQuant Brain数据字段信息（支持动态记录计数）\n", "    \n", "    改进点：\n", "    - 无搜索关键词时，自动获取实际记录总数\n", "    - 更好的URL构建方式\n", "    - 优化的分页处理逻辑\n", "    \"\"\"\n", "    # 从搜索范围中提取参数\n", "    instrument_type = searchScope['instrumentType']  # 金融工具类型\n", "    region = searchScope['region']                  # 地区\n", "    delay = searchScope['delay']                    # 数据延迟\n", "    universe = searchScope['universe']              # 股票池\n", "    \n", "    # 根据搜索条件构建API URL模板\n", "    if len(search) == 0:\n", "        # 无搜索关键词：获取指定数据集的所有字段\n", "        url_template = \"https://api.worldquantbrain.com/data-fields?\" \\\n", "            f\"&instrumentType={instrument_type}\" \\\n", "            f\"&region={region}&delay={str(delay)}&universe={universe}&dataset.id={dataset_id}&limit=50\" \\\n", "            \"&offset={x}\"\n", "        # 动态获取实际记录总数（更准确的分页）\n", "        count = s.get(url_template.format(x=0)).json()['count']\n", "    else:\n", "        # 有搜索关键词：按关键词搜索字段\n", "        url_template = \"https://api.worldquantbrain.com/data-fields?\" \\\n", "            f\"&instrumentType={instrument_type}\" \\\n", "            f\"&region={region}&delay={str(delay)}&universe={universe}&limit=50\" \\\n", "            f\"&search={search}\" \\\n", "            \"&offset={x}\"\n", "        count = 100  # 搜索结果使用固定限制\n", "\n", "    # 分页获取所有数据字段\n", "    datafields_list = []\n", "    for x in range(0, count, 50):  # 每页50条记录\n", "        datafields = s.get(url_template.format(x=x))\n", "        # 直接添加结果（假设请求总是成功）\n", "        datafields_list.append(datafields.json()['results'])\n", "    \n", "    # 展平嵌套列表结构\n", "    datafields_list_flat = [item for sublist in datafields_list for item in sublist]\n", "    \n", "    # 转换为DataFrame并返回\n", "    datafields_df = pd.DataFrame(datafields_list_flat)\n", "    return datafields_df  # 返回完整的数据字段DataFrame"]}, {"cell_type": "code", "execution_count": 4, "id": "8255e51d", "metadata": {}, "outputs": [], "source": ["# 配置数据字段搜索范围\n", "searchScope = {\n", "    'region': 'USA',           # 地区：美国市场\n", "    'delay': 1,               # 数据延迟：1天（实时性要求）\n", "    'universe': 'TOP3000',    # 股票池：美国前3000大股票\n", "    'instrumentType': 'EQUITY' # 金融工具类型：股票\n", "}\n", "\n", "# 获取fundamental6数据集的所有字段信息\n", "# fundamental6包含公司基本面数据（财务报表、估值指标等）\n", "fundamental6 = get_datafields(s=sess, searchScope=searchScope, dataset_id='fundamental6')"]}, {"cell_type": "code", "execution_count": 5, "id": "09477272", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>description</th>\n", "      <th>dataset</th>\n", "      <th>category</th>\n", "      <th>subcategory</th>\n", "      <th>region</th>\n", "      <th>delay</th>\n", "      <th>universe</th>\n", "      <th>type</th>\n", "      <th>coverage</th>\n", "      <th>userCount</th>\n", "      <th>alphaCount</th>\n", "      <th>themes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>assets</td>\n", "      <td>Assets - Total</td>\n", "      <td>{'id': 'fundamental6', 'name': 'Company Fundam...</td>\n", "      <td>{'id': 'fundamental', 'name': 'Fundamental'}</td>\n", "      <td>{'id': 'fundamental-fundamental-data', 'name':...</td>\n", "      <td>USA</td>\n", "      <td>1</td>\n", "      <td>TOP3000</td>\n", "      <td>MATRIX</td>\n", "      <td>0.9524</td>\n", "      <td>29316</td>\n", "      <td>101207</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>assets_curr</td>\n", "      <td>Current Assets - Total</td>\n", "      <td>{'id': 'fundamental6', 'name': 'Company Fundam...</td>\n", "      <td>{'id': 'fundamental', 'name': 'Fundamental'}</td>\n", "      <td>{'id': 'fundamental-fundamental-data', 'name':...</td>\n", "      <td>USA</td>\n", "      <td>1</td>\n", "      <td>TOP3000</td>\n", "      <td>MATRIX</td>\n", "      <td>0.7655</td>\n", "      <td>2610</td>\n", "      <td>13083</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>bookvalue_ps</td>\n", "      <td>Book Value Per Share</td>\n", "      <td>{'id': 'fundamental6', 'name': 'Company Fundam...</td>\n", "      <td>{'id': 'fundamental', 'name': 'Fundamental'}</td>\n", "      <td>{'id': 'fundamental-fundamental-data', 'name':...</td>\n", "      <td>USA</td>\n", "      <td>1</td>\n", "      <td>TOP3000</td>\n", "      <td>MATRIX</td>\n", "      <td>0.9754</td>\n", "      <td>2100</td>\n", "      <td>8895</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>capex</td>\n", "      <td>Capital Expenditures</td>\n", "      <td>{'id': 'fundamental6', 'name': 'Company Fundam...</td>\n", "      <td>{'id': 'fundamental', 'name': 'Fundamental'}</td>\n", "      <td>{'id': 'fundamental-fundamental-data', 'name':...</td>\n", "      <td>USA</td>\n", "      <td>1</td>\n", "      <td>TOP3000</td>\n", "      <td>MATRIX</td>\n", "      <td>0.9646</td>\n", "      <td>8689</td>\n", "      <td>22285</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>cash</td>\n", "      <td>Cash</td>\n", "      <td>{'id': 'fundamental6', 'name': 'Company Fundam...</td>\n", "      <td>{'id': 'fundamental', 'name': 'Fundamental'}</td>\n", "      <td>{'id': 'fundamental-fundamental-data', 'name':...</td>\n", "      <td>USA</td>\n", "      <td>1</td>\n", "      <td>TOP3000</td>\n", "      <td>MATRIX</td>\n", "      <td>0.7529</td>\n", "      <td>1946</td>\n", "      <td>10587</td>\n", "      <td>[]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             id             description  \\\n", "0        assets          Assets - Total   \n", "1   assets_curr  Current Assets - Total   \n", "2  bookvalue_ps    Book Value Per Share   \n", "3         capex    Capital Expenditures   \n", "4          cash                    Cash   \n", "\n", "                                             dataset  \\\n", "0  {'id': 'fundamental6', 'name': 'Company Fundam...   \n", "1  {'id': 'fundamental6', 'name': 'Company Fundam...   \n", "2  {'id': 'fundamental6', 'name': 'Company Fundam...   \n", "3  {'id': 'fundamental6', 'name': 'Company Fundam...   \n", "4  {'id': 'fundamental6', 'name': 'Company Fundam...   \n", "\n", "                                       category  \\\n", "0  {'id': 'fundamental', 'name': 'Fundamental'}   \n", "1  {'id': 'fundamental', 'name': 'Fundamental'}   \n", "2  {'id': 'fundamental', 'name': 'Fundamental'}   \n", "3  {'id': 'fundamental', 'name': 'Fundamental'}   \n", "4  {'id': 'fundamental', 'name': 'Fundamental'}   \n", "\n", "                                         subcategory region  delay universe  \\\n", "0  {'id': 'fundamental-fundamental-data', 'name':...    USA      1  TOP3000   \n", "1  {'id': 'fundamental-fundamental-data', 'name':...    USA      1  TOP3000   \n", "2  {'id': 'fundamental-fundamental-data', 'name':...    USA      1  TOP3000   \n", "3  {'id': 'fundamental-fundamental-data', 'name':...    USA      1  TOP3000   \n", "4  {'id': 'fundamental-fundamental-data', 'name':...    USA      1  TOP3000   \n", "\n", "     type  coverage  userCount  alphaCount themes  \n", "0  MATRIX    0.9524      29316      101207     []  \n", "1  MATRIX    0.7655       2610       13083     []  \n", "2  MATRIX    0.9754       2100        8895     []  \n", "3  MATRIX    0.9646       8689       22285     []  \n", "4  MATRIX    0.7529       1946       10587     []  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# 数据过滤：筛选MATRIX类型的数据字段\n", "# 过滤条件：只保留type为'MATRIX'的数据字段，这些是矩阵型数据，适合用于量化分析\n", "fundamental6 = fundamental6[fundamental6['type'] == 'MATRIX']\n", "# 显示过滤后的前5行数据，查看筛选结果\n", "fundamental6.head()"]}, {"cell_type": "code", "execution_count": 6, "id": "618bdefb", "metadata": {}, "outputs": [], "source": ["# 提取数据字段ID列表\n", "# 从过滤后的DataFrame中提取所有数据字段的ID，用于后续的Alpha策略构建\n", "# 这些ID代表各种财务指标，如assets（资产）、cash（现金）、revenue（收入）等\n", "datafields_list_fundamental6 = fundamental6['id'].values"]}, {"cell_type": "code", "execution_count": 7, "id": "bc4c17fd", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['assets', 'assets_curr', 'bookvalue_ps', 'capex', 'cash',\n", "       'cash_st', 'cashflow', 'cashflow_dividends', 'cashflow_fin',\n", "       'cashflow_invst', 'cashflow_op', 'cogs', 'current_ratio', 'debt',\n", "       'debt_lt', 'debt_st', 'depre_amort', 'ebit', 'ebitda', 'employee',\n", "       'enterprise_value', 'eps', 'equity', 'fnd6_acdo', 'fnd6_acodo',\n", "       'fnd6_acox', 'fnd6_acqgdwl', 'fnd6_acqintan',\n", "       'fnd6_adesinda_curcd', 'fnd6_aldo', 'fnd6_am', 'fnd6_aodo',\n", "       'fnd6_aox', 'fnd6_aqc', 'fnd6_aqi', 'fnd6_aqs', 'fnd6_beta',\n", "       'fnd6_capxv', 'fnd6_ceql', 'fnd6_ch', 'fnd6_ci', 'fnd6_cibegni',\n", "       'fnd6_cicurr', 'fnd6_cidergl', 'fnd6_cik', 'fnd6_cimii',\n", "       'fnd6_ciother', 'fnd6_cipen', 'fnd6_cisecgl', 'fnd6_citotal',\n", "       'fnd6_city', 'fnd6_cld2', 'fnd6_cld3', 'fnd6_cld4', 'fnd6_cld5',\n", "       'fnd6_cptmfmq_actq', 'fnd6_cptmfmq_atq', 'fnd6_cptmfmq_ceqq',\n", "       'fnd6_cptmfmq_dlttq', 'fnd6_cptmfmq_dpq', 'fnd6_cptmfmq_lctq',\n", "       'fnd6_cptmfmq_oibdpq', 'fnd6_cptmfmq_opepsq', 'fnd6_cptmfmq_saleq',\n", "       'fnd6_cptnewqv1300_actq', 'fnd6_cptnewqv1300_apq',\n", "       'fnd6_cptnewqv1300_atq', 'fnd6_cptnewqv1300_ceqq',\n", "       'fnd6_cptnewqv1300_dlttq', 'fnd6_cptnewqv1300_dpq',\n", "       'fnd6_cptnewqv1300_epsf12', 'fnd6_cptnewqv1300_epsfxq',\n", "       'fnd6_cptnewqv1300_epsx12', 'fnd6_cptnewqv1300_lctq',\n", "       'fnd6_cptnewqv1300_ltq', 'fnd6_cptnewqv1300_nopiq',\n", "       'fnd6_cptnewqv1300_oeps12', 'fnd6_cptnewqv1300_oiadpq',\n", "       'fnd6_cptnewqv1300_oibdpq', 'fnd6_cptnewqv1300_opepsq',\n", "       'fnd6_cptnewqv1300_rectq', 'fnd6_cptnewqv1300_req',\n", "       'fnd6_cptnewqv1300_saleq', 'fnd6_cptrank_gvkeymap', 'fnd6_cshpri',\n", "       'fnd6_cshr', 'fnd6_cshtr', 'fnd6_cshtrq', 'fnd6_cstkcv',\n", "       'fnd6_cstkcvq', 'fnd6_currencya_curcd',\n", "       'fnd6_currencyqv1300_curcd', 'fnd6_dc', 'fnd6_dclo', 'fnd6_dcpstk',\n", "       'fnd6_dcvsr', 'fnd6_dcvsub', 'fnd6_dcvt', 'fnd6_dd', 'fnd6_dd1',\n", "       'fnd6_dd1q', 'fnd6_dd2', 'fnd6_dd3', 'fnd6_dd4', 'fnd6_dd5',\n", "       'fnd6_dilavx', 'fnd6_dlcch', 'fnd6_dltis', 'fnd6_dlto',\n", "       'fnd6_dltp', 'fnd6_dltr', 'fnd6_dm', 'fnd6_dn', 'fnd6_donr',\n", "       'fnd6_dpvieb', 'fnd6_drc', 'fnd6_drlt', 'fnd6_ds', 'fnd6_dudd',\n", "       'fnd6_dvpa', 'fnd6_dxd2', 'fnd6_dxd3', 'fnd6_dxd4', 'fnd6_dxd5',\n", "       'fnd6_ein', 'fnd6_esopct', 'fnd6_esopnr', 'fnd6_esopr',\n", "       'fnd6_esubc', 'fnd6_exre', 'fnd6_fatb', 'fnd6_fatc', 'fnd6_fate',\n", "       'fnd6_fatl', 'fnd6_fatn', 'fnd6_fato', 'fnd6_fatp', 'fnd6_fiao',\n", "       'fnd6_fic', 'fnd6_fopo', 'fnd6_fopox', 'fnd6_fyrc', 'fnd6_ibmii',\n", "       'fnd6_idesindq_curcd', 'fnd6_idit', 'fnd6_incorp', 'fnd6_intan',\n", "       'fnd6_intc', 'fnd6_intpn', 'fnd6_invfg', 'fnd6_invo', 'fnd6_invrm',\n", "       'fnd6_invwip', 'fnd6_itcb', 'fnd6_itci', 'fnd6_ivaco',\n", "       'fnd6_ivaeq', 'fnd6_ivao', 'fnd6_ivch', 'fnd6_ivst', 'fnd6_ivstch',\n", "       'fnd6_lcox', 'fnd6_lcoxdr', 'fnd6_lifr', 'fnd6_lno', 'fnd6_loc',\n", "       'fnd6_lol2', 'fnd6_loxdr', 'fnd6_lqpl1', 'fnd6_lul3',\n", "       'fnd6_mfma1_aoloch', 'fnd6_mfma1_apalch', 'fnd6_mfma1_at',\n", "       'fnd6_mfma1_capx', 'fnd6_mfma1_csho', 'fnd6_mfma1_dp',\n", "       'fnd6_mfma1_dpc', 'fnd6_mfma1_invch', 'fnd6_mfma2_oancf',\n", "       'fnd6_mfma2_opeps', 'fnd6_mfma2_recch', 'fnd6_mfma2_revt',\n", "       'fnd6_mfma2_txach', 'fnd6_mfmq_cheq', 'fnd6_mfmq_cogsq',\n", "       'fnd6_mfmq_cshprq', 'fnd6_mfmq_dlcq', 'fnd6_mfmq_ibcomq',\n", "       'fnd6_mfmq_mibtq', 'fnd6_mfmq_piq', 'fnd6_mibn', 'fnd6_mibt',\n", "       'fnd6_mkvalt', 'fnd6_mkvaltq', 'fnd6_mrc1', 'fnd6_mrc2',\n", "       'fnd6_mrc3', 'fnd6_mrc4', 'fnd6_mrc5', 'fnd6_mrct', 'fnd6_mrcta',\n", "       'fnd6_msa', 'fnd6_newa1v1300_aco', 'fnd6_newa1v1300_acominc',\n", "       'fnd6_newa1v1300_act', 'fnd6_newa1v1300_ano', 'fnd6_newa1v1300_ao',\n", "       'fnd6_newa1v1300_aocidergl', 'fnd6_newa1v1300_aociother',\n", "       'fnd6_newa1v1300_aocipen', 'fnd6_newa1v1300_aol2',\n", "       'fnd6_newa1v1300_aoloch', 'fnd6_newa1v1300_ap',\n", "       'fnd6_newa1v1300_apalch', 'fnd6_newa1v1300_aqpl1',\n", "       'fnd6_newa1v1300_at', 'fnd6_newa1v1300_aul3',\n", "       'fnd6_newa1v1300_bkvlps', 'fnd6_newa1v1300_caps',\n", "       'fnd6_newa1v1300_capx', 'fnd6_newa1v1300_ceq',\n", "       'fnd6_newa1v1300_ceqt', 'fnd6_newa1v1300_che',\n", "       'fnd6_newa1v1300_chech', 'fnd6_newa1v1300_cogs',\n", "       'fnd6_newa1v1300_cshfd', 'fnd6_newa1v1300_cshi',\n", "       'fnd6_newa1v1300_csho', 'fnd6_newa1v1300_cstk',\n", "       'fnd6_newa1v1300_dcom', 'fnd6_newa1v1300_dlc',\n", "       'fnd6_newa1v1300_dltt', 'fnd6_newa1v1300_dp',\n", "       'fnd6_newa1v1300_dpact', 'fnd6_newa1v1300_dpc',\n", "       'fnd6_newa1v1300_dv', 'fnd6_newa1v1300_dvc', 'fnd6_newa1v1300_dvt',\n", "       'fnd6_newa1v1300_ebit', 'fnd6_newa1v1300_ebitda',\n", "       'fnd6_newa1v1300_emp', 'fnd6_newa1v1300_epsfi',\n", "       'fnd6_newa1v1300_epsfx', 'fnd6_newa1v1300_epspi',\n", "       'fnd6_newa1v1300_epspx', 'fnd6_newa1v1300_fca',\n", "       'fnd6_newa1v1300_fincf', 'fnd6_newa1v1300_gdwl',\n", "       'fnd6_newa1v1300_gp', 'fnd6_newa1v1300_ib',\n", "       'fnd6_newa1v1300_ibadj', 'fnd6_newa1v1300_ibc',\n", "       'fnd6_newa1v1300_ibcom', 'fnd6_newa1v1300_icapt',\n", "       'fnd6_newa1v1300_intano', 'fnd6_newa1v1300_invch',\n", "       'fnd6_newa1v1300_invt', 'fnd6_newa1v1300_ivncf',\n", "       'fnd6_newa1v1300_lco', 'fnd6_newa1v1300_lct', 'fnd6_newa1v1300_lo',\n", "       'fnd6_newa1v1300_lse', 'fnd6_newa1v1300_lt', 'fnd6_newa2v1300_mib',\n", "       'fnd6_newa2v1300_mii', 'fnd6_newa2v1300_ni',\n", "       'fnd6_newa2v1300_nopi', 'fnd6_newa2v1300_oancf',\n", "       'fnd6_newa2v1300_oiadp', 'fnd6_newa2v1300_oibdp',\n", "       'fnd6_newa2v1300_opeps', 'fnd6_newa2v1300_optexd',\n", "       'fnd6_newa2v1300_pi', 'fnd6_newa2v1300_ppegt',\n", "       'fnd6_newa2v1300_ppent', 'fnd6_newa2v1300_prsho',\n", "       'fnd6_newa2v1300_rdip', 'fnd6_newa2v1300_rdipa',\n", "       'fnd6_newa2v1300_rdipd', 'fnd6_newa2v1300_rdipeps',\n", "       'fnd6_newa2v1300_re', 'fnd6_newa2v1300_recch',\n", "       'fnd6_newa2v1300_rect', 'fnd6_newa2v1300_reuna',\n", "       'fnd6_newa2v1300_revt', 'fnd6_newa2v1300_sale',\n", "       'fnd6_newa2v1300_seq', 'fnd6_newa2v1300_seqo',\n", "       'fnd6_newa2v1300_spced', 'fnd6_newa2v1300_spceeps',\n", "       'fnd6_newa2v1300_spi', 'fnd6_newa2v1300_stkco',\n", "       'fnd6_newa2v1300_tstk', 'fnd6_newa2v1300_tstkn',\n", "       'fnd6_newa2v1300_txach', 'fnd6_newa2v1300_txdb',\n", "       'fnd6_newa2v1300_txditc', 'fnd6_newa2v1300_txp',\n", "       'fnd6_newa2v1300_txt', 'fnd6_newa2v1300_wcap',\n", "       'fnd6_newa2v1300_xidoc', 'fnd6_newa2v1300_xint',\n", "       'fnd6_newa2v1300_xoptd', 'fnd6_newa2v1300_xopteps',\n", "       'fnd6_newa2v1300_xrd', 'fnd6_newa2v1300_xsga', 'fnd6_newq_xoptdqp',\n", "       'fnd6_newq_xoptepsqp', 'fnd6_newq_xoptqp',\n", "       'fnd6_newqv1300_acomincq', 'fnd6_newqv1300_acoq',\n", "       'fnd6_newqv1300_altoq', 'fnd6_newqv1300_ancq',\n", "       'fnd6_newqv1300_anoq', 'fnd6_newqv1300_aociderglq',\n", "       'fnd6_newqv1300_aociotherq', 'fnd6_newqv1300_aocipenq',\n", "       'fnd6_newqv1300_aocisecglq', 'fnd6_newqv1300_aol2q',\n", "       'fnd6_newqv1300_aoq', 'fnd6_newqv1300_aqpl1q',\n", "       'fnd6_newqv1300_aul3q', 'fnd6_newqv1300_capsq',\n", "       'fnd6_newqv1300_chq', 'fnd6_newqv1300_cibegniq',\n", "       'fnd6_newqv1300_cicurrq', 'fnd6_newqv1300_ciderglq',\n", "       'fnd6_newqv1300_cimiiq', 'fnd6_newqv1300_ciotherq',\n", "       'fnd6_newqv1300_cipenq', 'fnd6_newqv1300_ciq',\n", "       'fnd6_newqv1300_cisecglq', 'fnd6_newqv1300_citotalq',\n", "       'fnd6_newqv1300_cogsq', 'fnd6_newqv1300_csh12q',\n", "       'fnd6_newqv1300_cshfdq', 'fnd6_newqv1300_cshiq',\n", "       'fnd6_newqv1300_cshopq', 'fnd6_newqv1300_cshoq',\n", "       'fnd6_newqv1300_cshprq', 'fnd6_newqv1300_cstkq',\n", "       'fnd6_newqv1300_dcomq', 'fnd6_newqv1300_dilavq',\n", "       'fnd6_newqv1300_dlcq', 'fnd6_newqv1300_dpactq',\n", "       'fnd6_newqv1300_drcq', 'fnd6_newqv1300_drltq',\n", "       'fnd6_newqv1300_epsfiq', 'fnd6_newqv1300_epspiq',\n", "       'fnd6_newqv1300_epspxq', 'fnd6_newqv1300_esopnrq',\n", "       'fnd6_newqv1300_esoprq', 'fnd6_newqv1300_fcaq',\n", "       'fnd6_newqv1300_gdwlq', 'fnd6_newqv1300_glcea12',\n", "       'fnd6_newqv1300_glced12', 'fnd6_newqv1300_glceeps12',\n", "       'fnd6_newqv1300_ibadj12', 'fnd6_newqv1300_ibadjq',\n", "       'fnd6_newqv1300_ibcomq', 'fnd6_newqv1300_ibmiiq',\n", "       'fnd6_newqv1300_ibq', 'fnd6_newqv1300_icaptq',\n", "       'fnd6_newqv1300_intanoq', 'fnd6_newqv1300_intanq',\n", "       'fnd6_newqv1300_invfgq', 'fnd6_newqv1300_invoq',\n", "       'fnd6_newqv1300_invrmq', 'fnd6_newqv1300_invtq',\n", "       'fnd6_newqv1300_invwipq', 'fnd6_newqv1300_ivltq',\n", "       'fnd6_newqv1300_ivstq', 'fnd6_newqv1300_lcoq',\n", "       'fnd6_newqv1300_lltq', 'fnd6_newqv1300_lnoq',\n", "       'fnd6_newqv1300_lol2q', 'fnd6_newqv1300_loq',\n", "       'fnd6_newqv1300_loxdrq', 'fnd6_newqv1300_lqpl1q',\n", "       'fnd6_newqv1300_lseq', 'fnd6_newqv1300_ltmibq',\n", "       'fnd6_newqv1300_lul3q', 'fnd6_newqv1300_mibnq',\n", "       'fnd6_newqv1300_mibtq', 'fnd6_newqv1300_miiq',\n", "       'fnd6_newqv1300_msaq', 'fnd6_newqv1300_oepf12',\n", "       'fnd6_newqv1300_oepsxq', 'fnd6_newqv1300_optfvgrq',\n", "       'fnd6_newqv1300_optrfrq', 'fnd6_newqv1300_piq',\n", "       'fnd6_newqv1300_pncq', 'fnd6_newqv1300_ppegtq',\n", "       'fnd6_newqv1300_ppentq', 'fnd6_newqv1300_prcaq',\n", "       'fnd6_newqv1300_prcdq', 'fnd6_newqv1300_prcepsq',\n", "       'fnd6_newqv1300_prcraq', 'fnd6_newqv1300_rcpq',\n", "       'fnd6_newqv1300_rdipaq', 'fnd6_newqv1300_rdipdq',\n", "       'fnd6_newqv1300_rdipepsq', 'fnd6_newqv1300_rdipq',\n", "       'fnd6_newqv1300_recdq', 'fnd6_newqv1300_rectaq',\n", "       'fnd6_newqv1300_rectoq', 'fnd6_newqv1300_rectrq',\n", "       'fnd6_newqv1300_reunaq', 'fnd6_newqv1300_revtq',\n", "       'fnd6_newqv1300_seqoq', 'fnd6_newqv1300_seqq',\n", "       'fnd6_newqv1300_spcedpq', 'fnd6_newqv1300_spcedq',\n", "       'fnd6_newqv1300_spceepsp12', 'fnd6_newqv1300_spceepspq',\n", "       'fnd6_newqv1300_spceepsq', 'fnd6_newqv1300_spcep12',\n", "       'fnd6_newqv1300_spcepd12', 'fnd6_newqv1300_spcepq',\n", "       'fnd6_newqv1300_spceq', 'fnd6_newqv1300_spiq',\n", "       'fnd6_newqv1300_stkcoq', 'fnd6_newqv1300_stkcpaq',\n", "       'fnd6_newqv1300_teqq', 'fnd6_newqv1300_tfvaq',\n", "       'fnd6_newqv1300_tfvceq', 'fnd6_newqv1300_tfvlq',\n", "       'fnd6_newqv1300_tstknq', 'fnd6_newqv1300_tstkq',\n", "       'fnd6_newqv1300_txdbaq', 'fnd6_newqv1300_txdbq',\n", "       'fnd6_newqv1300_txdiq', 'fnd6_newqv1300_txditcq',\n", "       'fnd6_newqv1300_txpq', 'fnd6_newqv1300_txtq',\n", "       'fnd6_newqv1300_txwq', 'fnd6_newqv1300_wcapq',\n", "       'fnd6_newqv1300_xintq', 'fnd6_newqv1300_xoprq',\n", "       'fnd6_newqv1300_xoptdq', 'fnd6_newqv1300_xoptepsq',\n", "       'fnd6_newqv1300_xoptq', 'fnd6_newqv1300_xrdq',\n", "       'fnd6_newqv1300_xsgaq', 'fnd6_niadj', 'fnd6_nopio', 'fnd6_np',\n", "       'fnd6_npq', 'fnd6_oprepsx', 'fnd6_optca', 'fnd6_optdr',\n", "       'fnd6_optdrq', 'fnd6_optex', 'fnd6_optfvgr', 'fnd6_optgr',\n", "       'fnd6_optlife', 'fnd6_optlifeq', 'fnd6_optosby', 'fnd6_optosey',\n", "       'fnd6_optprcby', 'fnd6_optprcca', 'fnd6_optprcex', 'fnd6_optprcey',\n", "       'fnd6_optprcgr', 'fnd6_optprcwa', 'fnd6_optrfr', 'fnd6_optvol',\n", "       'fnd6_optvolq', 'fnd6_pidom', 'fnd6_pifo', 'fnd6_pncdq',\n", "       'fnd6_pncepsq', 'fnd6_pnrsho', 'fnd6_ppeveb', 'fnd6_prcc',\n", "       'fnd6_prccq', 'fnd6_prch', 'fnd6_prchq', 'fnd6_prcl', 'fnd6_prclq',\n", "       'fnd6_prstkc', 'fnd6_pstkc', 'fnd6_pstkl', 'fnd6_pstkrv',\n", "       'fnd6_rank', 'fnd6_rea', 'fnd6_reajo', 'fnd6_recco', 'fnd6_recd',\n", "       'fnd6_recta', 'fnd6_rectr', 'fnd6_siv', 'fnd6_spce', 'fnd6_sppe',\n", "       'fnd6_sppiv', 'fnd6_sstk', 'fnd6_state', 'fnd6_stkcpa', 'fnd6_teq',\n", "       'fnd6_tfva', 'fnd6_tfvce', 'fnd6_tfvl', 'fnd6_tlcf', 'fnd6_tstkc',\n", "       'fnd6_txbco', 'fnd6_txbcof', 'fnd6_txc', 'fnd6_txdba',\n", "       'fnd6_txdbca', 'fnd6_txdbcl', 'fnd6_txdbclq', 'fnd6_txdc',\n", "       'fnd6_txdfed', 'fnd6_txdfo', 'fnd6_txdi', 'fnd6_txds',\n", "       'fnd6_txfed', 'fnd6_txfo', 'fnd6_txndb', 'fnd6_txndba',\n", "       'fnd6_txndbl', 'fnd6_txndbr', 'fnd6_txo', 'fnd6_txpd', 'fnd6_txr',\n", "       'fnd6_txs', 'fnd6_txtubadjust', 'fnd6_txtubbegin', 'fnd6_txtubend',\n", "       'fnd6_txtubposdec', 'fnd6_txtubposinc', 'fnd6_txtubpospdec',\n", "       'fnd6_txtubpospinc', 'fnd6_txtubsettle', 'fnd6_txtubsoflimit',\n", "       'fnd6_txtubtxtr', 'fnd6_txtubxintbs', 'fnd6_txtubxintis',\n", "       'fnd6_txw', 'fnd6_weburl', 'fnd6_xacc', 'fnd6_xaccq', 'fnd6_xad',\n", "       'fnd6_xintopt', 'fnd6_xopr', 'fnd6_xpp', 'fnd6_xpr', 'fnd6_xrent',\n", "       'fnd6_zipcode', 'goodwill', 'income', 'income_beforeextra',\n", "       'income_tax', 'interest_expense', 'inventory',\n", "       'inventory_turnover', 'invested_capital', 'liabilities',\n", "       'liabilities_curr', 'operating_expense', 'operating_income',\n", "       'ppent', 'pretax_income', 'rd_expense', 'receivable',\n", "       'retained_earnings', 'return_assets', 'return_equity', 'revenue',\n", "       'sales', 'sales_growth', 'sales_ps', 'sga_expense',\n", "       'working_capital'], dtype=object)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# 显示所有可用的财务数据字段ID\n", "# 输出包含所有基础财务指标的数组，这些字段可用于构建量化投资策略\n", "# 包括基本财务数据（如assets、cash、revenue）和复合指标（如各种比率和增长率）\n", "datafields_list_fundamental6"]}, {"cell_type": "code", "execution_count": 8, "id": "30c77033", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在将如下Alpha表达式与setting封装\n", "group_rank(assets/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(assets_curr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(bookvalue_ps/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(capex/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(cash/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(cash_st/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(cashflow/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(cashflow_dividends/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(cashflow_fin/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(cashflow_invst/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(cashflow_op/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(cogs/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(current_ratio/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(debt/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(debt_lt/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(debt_st/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(depre_amort/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(ebit/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(ebitda/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(employee/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(enterprise_value/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(eps/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(equity/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_acdo/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_acodo/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_acox/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_acqgdwl/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_acqintan/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_adesinda_curcd/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_aldo/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_am/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_aodo/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_aox/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_aqc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_aqi/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_aqs/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_beta/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_capxv/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_ceql/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_ch/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_ci/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cibegni/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cicurr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cidergl/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cik/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cimii/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_ciother/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cipen/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cisecgl/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_citotal/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_city/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cld2/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cld3/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cld4/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cld5/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptmfmq_actq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptmfmq_atq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptmfmq_ceqq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptmfmq_dlttq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptmfmq_dpq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptmfmq_lctq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptmfmq_oibdpq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptmfmq_opepsq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptmfmq_saleq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptnewqv1300_actq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptnewqv1300_apq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptnewqv1300_atq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptnewqv1300_ceqq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptnewqv1300_dlttq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptnewqv1300_dpq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptnewqv1300_epsf12/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptnewqv1300_epsfxq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptnewqv1300_epsx12/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptnewqv1300_lctq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptnewqv1300_ltq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptnewqv1300_nopiq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptnewqv1300_oeps12/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptnewqv1300_oiadpq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptnewqv1300_oibdpq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptnewqv1300_opepsq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptnewqv1300_rectq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptnewqv1300_req/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptnewqv1300_saleq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cptrank_gvkeymap/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cshpri/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cshr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cshtr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cshtrq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cstkcv/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_cstkcvq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_currencya_curcd/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_currencyqv1300_curcd/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dclo/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dcpstk/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dcvsr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dcvsub/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dcvt/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dd/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dd1/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dd1q/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dd2/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dd3/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dd4/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dd5/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dilavx/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dlcch/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dltis/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dlto/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dltp/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dltr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dm/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dn/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_donr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dpvieb/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_drc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_drlt/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_ds/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dudd/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dvpa/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dxd2/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dxd3/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dxd4/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_dxd5/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_ein/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_esopct/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_esopnr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_esopr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_esubc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_exre/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_fatb/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_fatc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_fate/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_fatl/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_fatn/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_fato/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_fatp/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_fiao/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_fic/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_fopo/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_fopox/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_fyrc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_ibmii/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_idesindq_curcd/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_idit/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_incorp/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_intan/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_intc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_intpn/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_invfg/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_invo/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_invrm/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_invwip/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_itcb/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_itci/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_ivaco/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_ivaeq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_ivao/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_ivch/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_ivst/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_ivstch/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_lcox/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_lcoxdr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_lifr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_lno/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_loc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_lol2/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_loxdr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_lqpl1/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_lul3/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mfma1_aoloch/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mfma1_apalch/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mfma1_at/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mfma1_capx/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mfma1_csho/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mfma1_dp/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mfma1_dpc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mfma1_invch/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mfma2_oancf/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mfma2_opeps/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mfma2_recch/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mfma2_revt/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mfma2_txach/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mfmq_cheq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mfmq_cogsq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mfmq_cshprq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mfmq_dlcq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mfmq_ibcomq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mfmq_mibtq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mfmq_piq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mibn/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mibt/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mkvalt/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mkvaltq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mrc1/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mrc2/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mrc3/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mrc4/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mrc5/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mrct/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_mrcta/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_msa/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_aco/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_acominc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_act/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_ano/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_ao/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_aocidergl/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_aociother/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_aocipen/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_aol2/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_aoloch/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_ap/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_apalch/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_aqpl1/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_at/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_aul3/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_bkvlps/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_caps/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_capx/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_ceq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_ceqt/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_che/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_chech/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_cogs/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_cshfd/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_cshi/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_csho/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_cstk/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_dcom/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_dlc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_dltt/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_dp/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_dpact/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_dpc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_dv/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_dvc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_dvt/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_ebit/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_ebitda/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_emp/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_epsfi/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_epsfx/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_epspi/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_epspx/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_fca/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_fincf/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_gdwl/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_gp/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_ib/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_ibadj/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_ibc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_ibcom/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_icapt/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_intano/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_invch/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_invt/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_ivncf/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_lco/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_lct/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_lo/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_lse/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa1v1300_lt/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_mib/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_mii/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_ni/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_nopi/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_oancf/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_oiadp/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_oibdp/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_opeps/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_optexd/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_pi/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_ppegt/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_ppent/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_prsho/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_rdip/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_rdipa/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_rdipd/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_rdipeps/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_re/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_recch/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_rect/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_reuna/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_revt/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_sale/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_seq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_seqo/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_spced/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_spceeps/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_spi/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_stkco/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_tstk/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_tstkn/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_txach/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_txdb/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_txditc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_txp/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_txt/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_wcap/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_xidoc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_xint/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_xoptd/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_xopteps/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_xrd/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newa2v1300_xsga/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newq_xoptdqp/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newq_xoptepsqp/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newq_xoptqp/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_acomincq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_acoq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_altoq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_ancq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_anoq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_aociderglq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_aociotherq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_aocipenq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_aocisecglq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_aol2q/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_aoq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_aqpl1q/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_aul3q/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_capsq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_chq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_cibegniq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_cicurrq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_ciderglq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_cimiiq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_ciotherq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_cipenq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_ciq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_cisecglq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_citotalq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_cogsq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_csh12q/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_cshfdq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_cshiq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_cshopq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_cshoq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_cshprq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_cstkq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_dcomq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_dilavq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_dlcq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_dpactq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_drcq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_drltq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_epsfiq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_epspiq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_epspxq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_esopnrq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_esoprq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_fcaq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_gdwlq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_glcea12/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_glced12/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_glceeps12/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_ibadj12/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_ibadjq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_ibcomq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_ibmiiq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_ibq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_icaptq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_intanoq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_intanq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_invfgq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_invoq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_invrmq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_invtq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_invwipq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_ivltq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_ivstq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_lcoq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_lltq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_lnoq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_lol2q/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_loq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_loxdrq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_lqpl1q/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_lseq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_ltmibq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_lul3q/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_mibnq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_mibtq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_miiq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_msaq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_oepf12/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_oepsxq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_optfvgrq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_optrfrq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_piq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_pncq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_ppegtq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_ppentq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_prcaq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_prcdq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_prcepsq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_prcraq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_rcpq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_rdipaq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_rdipdq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_rdipepsq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_rdipq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_recdq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_rectaq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_rectoq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_rectrq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_reunaq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_revtq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_seqoq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_seqq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_spcedpq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_spcedq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_spceepsp12/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_spceepspq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_spceepsq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_spcep12/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_spcepd12/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_spcepq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_spceq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_spiq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_stkcoq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_stkcpaq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_teqq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_tfvaq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_tfvceq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_tfvlq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_tstknq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_tstkq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_txdbaq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_txdbq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_txdiq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_txditcq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_txpq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_txtq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_txwq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_wcapq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_xintq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_xoprq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_xoptdq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_xoptepsq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_xoptq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_xrdq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_newqv1300_xsgaq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_niadj/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_nopio/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_np/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_npq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_oprepsx/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_optca/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_optdr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_optdrq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_optex/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_optfvgr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_optgr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_optlife/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_optlifeq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_optosby/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_optosey/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_optprcby/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_optprcca/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_optprcex/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_optprcey/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_optprcgr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_optprcwa/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_optrfr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_optvol/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_optvolq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_pidom/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_pifo/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_pncdq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_pncepsq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_pnrsho/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_ppeveb/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_prcc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_prccq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_prch/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_prchq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_prcl/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_prclq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_prstkc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_pstkc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_pstkl/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_pstkrv/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_rank/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_rea/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_reajo/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_recco/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_recd/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_recta/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_rectr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_siv/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_spce/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_sppe/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_sppiv/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_sstk/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_state/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_stkcpa/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_teq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_tfva/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_tfvce/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_tfvl/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_tlcf/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_tstkc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txbco/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txbcof/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txdba/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txdbca/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txdbcl/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txdbclq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txdc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txdfed/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txdfo/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txdi/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txds/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txfed/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txfo/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txndb/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txndba/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txndbl/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txndbr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txo/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txpd/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txs/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txtubadjust/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txtubbegin/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txtubend/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txtubposdec/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txtubposinc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txtubpospdec/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txtubpospinc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txtubsettle/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txtubsoflimit/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txtubtxtr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txtubxintbs/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txtubxintis/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_txw/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_weburl/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_xacc/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_xaccq/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_xad/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_xintopt/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_xopr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_xpp/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_xpr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_xrent/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(fnd6_zipcode/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(goodwill/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(income/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(income_beforeextra/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(income_tax/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(interest_expense/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(inventory/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(inventory_turnover/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(invested_capital/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(liabilities/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(liabilities_curr/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(operating_expense/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(operating_income/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(ppent/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(pretax_income/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(rd_expense/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(receivable/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(retained_earnings/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(return_assets/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(return_equity/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(revenue/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(sales/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(sales_growth/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(sales_ps/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(sga_expense/cap, subindustry)\n", "正在将如下Alpha表达式与setting封装\n", "group_rank(working_capital/cap, subindustry)\n", "there are 574 Alphas to simulate\n"]}], "source": ["alpha_list = []\n", "\n", "for datafield in datafields_list_fundamental6:\n", "    print(f\"正在将如下Alpha表达式与setting封装\")\n", "    alpha_expression = f\"group_rank({datafield}/cap, subindustry)\"\n", "    print(alpha_expression)\n", "    simulation_data = {\n", "        'type': 'REGULAR',\n", "        'settings': {\n", "            'instrumentType': 'EQUITY',\n", "            'region': 'USA',\n", "            'universe': 'TOP3000',\n", "            'delay': 1,\n", "            'decay': 0,\n", "            'neutralization': 'SUBINDUSTRY',\n", "            'truncation': 0.08,\n", "            'pasteurization': 'ON',\n", "            'unitHandling': 'VERIFY',\n", "            'nanHandling': 'ON',\n", "            'language': 'FASTEXPR',\n", "            'visualization': <PERSON><PERSON><PERSON>,\n", "        },\n", "        'regular': alpha_expression\n", "    }\n", "    \n", "    alpha_list.append(simulation_data)\n", "\n", "print(f\"there are {len(alpha_list)} Alphas to simulate\")"]}, {"cell_type": "code", "execution_count": 9, "id": "49f21fcf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["no location, sleep for 10 seconds and try next alpha\n", "no location, sleep for 10 seconds and try next alpha\n", "no location, sleep for 10 seconds and try next alpha\n", "ojxk28k\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[9], line 4\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m \u001b[38;5;21;01mtime\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m sleep\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m alpha \u001b[38;5;129;01min\u001b[39;00m alpha_list:\n\u001b[0;32m----> 4\u001b[0m     sim_resp \u001b[38;5;241m=\u001b[39m \u001b[43msess\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpost\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m      5\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mhttps://api.worldquantbrain.com/simulations\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m      6\u001b[0m \u001b[43m        \u001b[49m\u001b[43mjson\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43malpha\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m      7\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m      9\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m     10\u001b[0m         sim_progress_url \u001b[38;5;241m=\u001b[39m sim_resp\u001b[38;5;241m.\u001b[39mheaders[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mlocation\u001b[39m\u001b[38;5;124m'\u001b[39m]\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/requests/sessions.py:637\u001b[0m, in \u001b[0;36mSession.post\u001b[0;34m(self, url, data, json, **kwargs)\u001b[0m\n\u001b[1;32m    626\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mpost\u001b[39m(\u001b[38;5;28mself\u001b[39m, url, data\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, json\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m    627\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124mr\u001b[39m\u001b[38;5;124;03m\"\"\"Sends a POST request. Returns :class:`Response` object.\u001b[39;00m\n\u001b[1;32m    628\u001b[0m \n\u001b[1;32m    629\u001b[0m \u001b[38;5;124;03m    :param url: URL for the new :class:`Request` object.\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    634\u001b[0m \u001b[38;5;124;03m    :rtype: requests.Response\u001b[39;00m\n\u001b[1;32m    635\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 637\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mPOST\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mjson\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mjson\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/requests/sessions.py:589\u001b[0m, in \u001b[0;36mSession.request\u001b[0;34m(self, method, url, params, data, headers, cookies, files, auth, timeout, allow_redirects, proxies, hooks, stream, verify, cert, json)\u001b[0m\n\u001b[1;32m    584\u001b[0m send_kwargs \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m    585\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtimeout\u001b[39m\u001b[38;5;124m\"\u001b[39m: timeout,\n\u001b[1;32m    586\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mallow_redirects\u001b[39m\u001b[38;5;124m\"\u001b[39m: allow_redirects,\n\u001b[1;32m    587\u001b[0m }\n\u001b[1;32m    588\u001b[0m send_kwargs\u001b[38;5;241m.\u001b[39mupdate(settings)\n\u001b[0;32m--> 589\u001b[0m resp \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprep\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43msend_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    591\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m resp\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/requests/sessions.py:703\u001b[0m, in \u001b[0;36mSession.send\u001b[0;34m(self, request, **kwargs)\u001b[0m\n\u001b[1;32m    700\u001b[0m start \u001b[38;5;241m=\u001b[39m preferred_clock()\n\u001b[1;32m    702\u001b[0m \u001b[38;5;66;03m# Send the request\u001b[39;00m\n\u001b[0;32m--> 703\u001b[0m r \u001b[38;5;241m=\u001b[39m \u001b[43madapter\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    705\u001b[0m \u001b[38;5;66;03m# Total elapsed time of the request (approximately)\u001b[39;00m\n\u001b[1;32m    706\u001b[0m elapsed \u001b[38;5;241m=\u001b[39m preferred_clock() \u001b[38;5;241m-\u001b[39m start\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/requests/adapters.py:486\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[0;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[1;32m    483\u001b[0m     timeout \u001b[38;5;241m=\u001b[39m TimeoutSauce(connect\u001b[38;5;241m=\u001b[39mtimeout, read\u001b[38;5;241m=\u001b[39mtimeout)\n\u001b[1;32m    485\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 486\u001b[0m     resp \u001b[38;5;241m=\u001b[39m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    487\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    488\u001b[0m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    489\u001b[0m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    490\u001b[0m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    491\u001b[0m \u001b[43m        \u001b[49m\u001b[43mredirect\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    492\u001b[0m \u001b[43m        \u001b[49m\u001b[43massert_same_host\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    493\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    494\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    495\u001b[0m \u001b[43m        \u001b[49m\u001b[43mretries\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmax_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    496\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    497\u001b[0m \u001b[43m        \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    498\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    500\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (ProtocolError, \u001b[38;5;167;01mOSError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[1;32m    501\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m(err, request\u001b[38;5;241m=\u001b[39mrequest)\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/urllib3/connectionpool.py:787\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[0;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[0m\n\u001b[1;32m    784\u001b[0m response_conn \u001b[38;5;241m=\u001b[39m conn \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m release_conn \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    786\u001b[0m \u001b[38;5;66;03m# Make the request on the HTTPConnection object\u001b[39;00m\n\u001b[0;32m--> 787\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    788\u001b[0m \u001b[43m    \u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    789\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    790\u001b[0m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    791\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout_obj\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    792\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    793\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    794\u001b[0m \u001b[43m    \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    795\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretries\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mretries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    796\u001b[0m \u001b[43m    \u001b[49m\u001b[43mresponse_conn\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mresponse_conn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    797\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpreload_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    798\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdecode_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    799\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mresponse_kw\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    800\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    802\u001b[0m \u001b[38;5;66;03m# Everything went great!\u001b[39;00m\n\u001b[1;32m    803\u001b[0m clean_exit \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/urllib3/connectionpool.py:534\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[0;34m(self, conn, method, url, body, headers, retries, timeout, chunked, response_conn, preload_content, decode_content, enforce_content_length)\u001b[0m\n\u001b[1;32m    532\u001b[0m \u001b[38;5;66;03m# Receive the response from the server\u001b[39;00m\n\u001b[1;32m    533\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 534\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgetresponse\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    535\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (BaseSSLError, \u001b[38;5;167;01mOSError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    536\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_raise_timeout(err\u001b[38;5;241m=\u001b[39me, url\u001b[38;5;241m=\u001b[39murl, timeout_value\u001b[38;5;241m=\u001b[39mread_timeout)\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/urllib3/connection.py:516\u001b[0m, in \u001b[0;36mHTTPConnection.getresponse\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    513\u001b[0m _shutdown \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mgetattr\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msock, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mshutdown\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[1;32m    515\u001b[0m \u001b[38;5;66;03m# Get the response from http.client.HTTPConnection\u001b[39;00m\n\u001b[0;32m--> 516\u001b[0m httplib_response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgetresponse\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    518\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    519\u001b[0m     assert_header_parsing(httplib_response\u001b[38;5;241m.\u001b[39mmsg)\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/client.py:1411\u001b[0m, in \u001b[0;36mHTTPConnection.getresponse\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1409\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1410\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1411\u001b[0m         \u001b[43mresponse\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbegin\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1412\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m:\n\u001b[1;32m   1413\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mclose()\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/client.py:324\u001b[0m, in \u001b[0;36mHTTPResponse.begin\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    322\u001b[0m \u001b[38;5;66;03m# read until we get a non-100 response\u001b[39;00m\n\u001b[1;32m    323\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[0;32m--> 324\u001b[0m     version, status, reason \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_read_status\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    325\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m status \u001b[38;5;241m!=\u001b[39m CONTINUE:\n\u001b[1;32m    326\u001b[0m         \u001b[38;5;28;01mbreak\u001b[39;00m\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/client.py:285\u001b[0m, in \u001b[0;36mHTTPResponse._read_status\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    284\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_read_status\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[0;32m--> 285\u001b[0m     line \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mstr\u001b[39m(\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mreadline\u001b[49m\u001b[43m(\u001b[49m\u001b[43m_MAXLINE\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m)\u001b[49m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124miso-8859-1\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    286\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(line) \u001b[38;5;241m>\u001b[39m _MAXLINE:\n\u001b[1;32m    287\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m LineTooLong(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstatus line\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/socket.py:707\u001b[0m, in \u001b[0;36mSocketIO.readinto\u001b[0;34m(self, b)\u001b[0m\n\u001b[1;32m    705\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[1;32m    706\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 707\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sock\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrecv_into\u001b[49m\u001b[43m(\u001b[49m\u001b[43mb\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    708\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m timeout:\n\u001b[1;32m    709\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_timeout_occurred \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ssl.py:1249\u001b[0m, in \u001b[0;36mSSLSocket.recv_into\u001b[0;34m(self, buffer, nbytes, flags)\u001b[0m\n\u001b[1;32m   1245\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m flags \u001b[38;5;241m!=\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m   1246\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m   1247\u001b[0m           \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnon-zero flags not allowed in calls to recv_into() on \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m\n\u001b[1;32m   1248\u001b[0m           \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m)\n\u001b[0;32m-> 1249\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnbytes\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbuffer\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1250\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1251\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28msuper\u001b[39m()\u001b[38;5;241m.\u001b[39mrecv_into(buffer, nbytes, flags)\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ssl.py:1105\u001b[0m, in \u001b[0;36mSSLSocket.read\u001b[0;34m(self, len, buffer)\u001b[0m\n\u001b[1;32m   1103\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1104\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m buffer \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m-> 1105\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sslobj\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mlen\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbuffer\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1106\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1107\u001b[0m         \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_sslobj\u001b[38;5;241m.\u001b[39mread(\u001b[38;5;28mlen\u001b[39m)\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["from time import sleep\n", "\n", "for alpha in alpha_list:\n", "    sim_resp = sess.post(\n", "        'https://api.worldquantbrain.com/simulations',\n", "        json=alpha,\n", "    )\n", "    \n", "    try:\n", "        sim_progress_url = sim_resp.headers['location']\n", "        while True:\n", "            sim_progress_resp = sess.get(sim_progress_url)\n", "            retry_after_sec = float(sim_progress_resp.headers.get(\"Retry-After\", 0))\n", "            if retry_after_sec == 0:  # simulation done!\n", "                break\n", "            sleep(retry_after_sec)\n", "        \n", "        alpha_id = sim_progress_resp.json()[\"alpha\"]  # the final simulation result\n", "        print(alpha_id)\n", "        \n", "    except:\n", "        print(\"no location, sleep for 10 seconds and try next alpha\")\n", "        sleep(10)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 5}