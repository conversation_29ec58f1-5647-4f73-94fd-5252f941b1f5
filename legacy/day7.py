# WorldQuant Brain Alpha策略智能批量生成系统 (Day7)
# 基于day6.py的完整功能，新增运算符组合配置化功能
# 主要改进：通过combinations_config.json实现经典运算符组合的批量测试

import requests
import json
import os
import logging
from datetime import datetime
from os.path import expanduser
from time import sleep
from requests.auth import HTTPBasicAuth

# ==================== 配置常量 ====================

# 文件路径配置（完全继承day6）
QUALIFIED_ALPHA_FILE = 'qualified_alphas.txt'
SIMULATION_LOG_FILE = 'simulation.log'
CREDENTIALS_FILE = 'brain.txt'
DATASETS_CONFIG_FILE = 'datasets_config.json'
OPERATORS_CONFIG_FILE = 'operators_config.json'
COMBINATIONS_CONFIG_FILE = 'combinations_config.json'  # Day7新增

# ==================== 完全继承Day6的所有函数 ====================

def load_datasets_config():
    """加载数据集配置（继承自day6.py）"""
    try:
        with open(DATASETS_CONFIG_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 找不到数据集配置文件: {DATASETS_CONFIG_FILE}")
        return None
    except json.JSONDecodeError:
        print(f"❌ 数据集配置文件格式错误: {DATASETS_CONFIG_FILE}")
        return None

def load_operators_config():
    """加载运算符配置（继承自day6.py）"""
    try:
        with open(OPERATORS_CONFIG_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 找不到运算符配置文件: {OPERATORS_CONFIG_FILE}")
        return None
    except json.JSONDecodeError:
        print(f"❌ 运算符配置文件格式错误: {OPERATORS_CONFIG_FILE}")
        return None

def load_combinations_config():
    """加载组合配置（Day7新增功能）"""
    try:
        with open(COMBINATIONS_CONFIG_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 找不到组合配置文件: {COMBINATIONS_CONFIG_FILE}")
        return None
    except json.JSONDecodeError:
        print(f"❌ 组合配置文件格式错误: {COMBINATIONS_CONFIG_FILE}")
        return None

class DatasetManager:
    """数据集管理器（完全继承自day6.py）"""
    
    def __init__(self):
        self.config = load_datasets_config()
        if not self.config:
            raise Exception("无法加载数据集配置")
        self.datasets = self.config['datasets']
        self.default_config = self.config['default_config']
    
    def list_all_datasets(self):
        """列出所有可用数据集（继承自day6.py）"""
        print("\n📊 可用数据集列表 (共14个):")
        print("="*80)
        
        for i, (dataset_id, config) in enumerate(self.datasets.items(), 1):
            print(f"{i:2d}. {dataset_id:<15} - {config['name']}")
            print(f"    类别: {config['category']}")
            print(f"    覆盖率: {config['coverage']:<4} | 字段数: {config['field_count']:<3} | 描述: {config['description'][:60]}...")
            print()
    
    def get_dataset_by_choice(self, choice):
        """根据用户选择获取数据集配置（继承自day6.py）"""
        try:
            choice_num = int(choice)
            dataset_ids = list(self.datasets.keys())
            
            if 1 <= choice_num <= len(dataset_ids):
                dataset_id = dataset_ids[choice_num - 1]
                config = self.datasets[dataset_id]
                
                best_universe = self._select_best_universe(config['universes'])
                best_delay = str(config['delays'][0])
                
                search_scope = {
                    'instrumentType': self.default_config['instrumentType'],
                    'region': config['regions'][0],
                    'delay': best_delay,
                    'universe': best_universe
                }
                
                return dataset_id, search_scope, config
            else:
                return None, None, None
                
        except ValueError:
            return None, None, None
    
    def _select_best_universe(self, universes):
        """选择最佳股票池（继承自day6.py）"""
        priority = self.default_config['universe_priority']
        for universe in priority:
            if universe in universes:
                return universe
        return universes[0] if universes else 'TOP1000'
    
    def get_dataset_info(self, dataset_id):
        """获取数据集详细信息（继承自day6.py）"""
        return self.datasets.get(dataset_id, None)

class OperatorLibrary:
    """运算符库管理器（完全继承自day6.py）"""
    
    def __init__(self):
        self.config = load_operators_config()
        if not self.config:
            raise Exception("无法加载运算符配置")
        self.operators = self.config['operator_categories']
        self.operator_sets = self.config['operator_sets']
    
    def get_operator_set(self, set_name='basic'):
        """获取运算符组合（继承自day6.py）"""
        return self.operator_sets.get(set_name, self.operator_sets['basic'])

class CombinationManager:
    """运算符组合管理器（Day7新增类）"""
    
    def __init__(self):
        self.config = load_combinations_config()
        if not self.config:
            raise Exception("无法加载组合配置")
        self.combination_sets = self.config['combination_sets']
        self.custom_combinations = self.config['custom_combinations']
        self.metadata = self.config['combination_metadata']
    
    def list_all_combinations(self):
        """列出所有可用组合"""
        print(f"\n🔧 可用运算符组合列表 (共{len(self.combination_sets)}个预设组合):")
        print("="*80)
        
        for i, (combo_id, config) in enumerate(self.combination_sets.items(), 1):
            print(f"{i:2d}. {config['name']}")
            print(f"    描述: {config['description']}")
            print(f"    分组运算符: {config['group_ops']}")
            print(f"    时间序列: {config['ts_ops']}")
            print(f"    周期: {config['periods']}")
            print()
    
    def get_combination_by_choice(self, choice):
        """根据用户选择获取组合配置"""
        try:
            choice_num = int(choice)
            combo_ids = list(self.combination_sets.keys())
            
            if 1 <= choice_num <= len(combo_ids):
                combo_id = combo_ids[choice_num - 1]
                config = self.combination_sets[combo_id]
                return combo_id, config
            else:
                return None, None
                
        except ValueError:
            return None, None
    
    def get_combination_info(self, combo_id):
        """获取组合详细信息"""
        return self.combination_sets.get(combo_id, None)

def sign_in():
    """WorldQuant Brain API认证函数（完全继承自day6.py）"""
    try:
        with open(expanduser(CREDENTIALS_FILE)) as f:
            credentials = json.load(f)
        
        username, password = credentials
        sess = requests.Session()
        sess.auth = HTTPBasicAuth(username, password)
        
        response = sess.post('https://api.worldquantbrain.com/authentication')
        
        if response.status_code == 201:
            print("✅ WorldQuant Brain API认证成功")
        else:
            print(f"❌ 认证失败，状态码: {response.status_code}")
        
        return sess
        
    except FileNotFoundError:
        print("❌ 错误：找不到brain.txt凭据文件")
        print("请确保在项目根目录下创建brain.txt文件，格式为: [\"username\", \"password\"]")
        raise
    except json.JSONDecodeError:
        print("❌ 错误：brain.txt文件格式不正确")
        print("正确格式应为: [\"<EMAIL>\", \"your_password\"]")
        raise
    except Exception as e:
        print(f"❌ 认证过程发生错误: {str(e)}")
        raise

def get_datafields(s, searchScope, dataset_id='', search=''):
    """获取WorldQuant Brain数据字段信息（完全继承自day6.py）"""
    import pandas as pd
    
    instrument_type = searchScope['instrumentType']
    region = searchScope['region']
    delay = searchScope['delay']
    universe = searchScope['universe']
    
    print(f"🔍 开始获取数据字段...")
    print(f"   数据集ID: {dataset_id if dataset_id else '全部'}")
    print(f"   市场范围: {region} {universe}")
    
    if len(search) == 0:
        url_template = "https://api.worldquantbrain.com/data-fields?" + \
                       f"&instrumentType={instrument_type}" + \
                       f"&region={region}&delay={str(delay)}&universe={universe}&dataset.id={dataset_id}&limit=50" + \
                       "&offset={x}"
        
        initial_response = s.get(url_template.format(x=0))
        if initial_response.status_code == 200:
            count = initial_response.json()['count']
            print(f"   总记录数: {count}")
        else:
            print(f"❌ 获取记录数失败，状态码: {initial_response.status_code}")
            count = 0
    else:
        url_template = "https://api.worldquantbrain.com/data-fields?" + \
                       f"&instrumentType={instrument_type}" + \
                       f"&region={region}&delay={str(delay)}&universe={universe}&limit=50" + \
                       f"&search={search}" + \
                       "&offset={x}"
        count = 100
    
    datafields_list = []
    pages_processed = 0
    
    for x in range(0, count, 50):
        try:
            datafields = s.get(url_template.format(x=x))
            if datafields.status_code == 200:
                results = datafields.json()['results']
                datafields_list.append(results)
                pages_processed += 1
                print(f"   已处理第 {pages_processed} 页，获取 {len(results)} 条记录")
            else:
                print(f"❌ 第 {pages_processed + 1} 页请求失败，状态码: {datafields.status_code}")
                break
        except Exception as e:
            print(f"❌ 处理第 {pages_processed + 1} 页时发生错误: {e}")
            break
    
    datafields_list_flat = [item for sublist in datafields_list for item in sublist]
    datafields_df = pd.DataFrame(datafields_list_flat)
    
    print(f"✅ 数据字段获取完成，共 {len(datafields_df)} 条记录")
    return datafields_df

# ==================== 继承Day6的数据集选择功能 ====================

def choose_by_category(dataset_manager):
    """按7大类选择数据集（继承自day6.py）"""
    
    # 基于datasets_config.json的category字段分类
    CATEGORIES = {
        1: {'name': '📊 分析师研究', 'pattern': 'Analyst'},
        2: {'name': '💰 基本面分析', 'pattern': 'Fundamental'}, 
        3: {'name': '🤖 量化模型', 'pattern': 'Model'},
        4: {'name': '📰 新闻事件', 'pattern': 'News'},
        5: {'name': '📈 期权市场', 'pattern': 'Option'},
        6: {'name': '💹 价格成交量', 'pattern': 'Price Volume'},
        7: {'name': '📱 社交媒体', 'pattern': 'Social Media'}
    }
    
    print("\n📊 按数据类别选择:")
    print("="*50)
    for num, info in CATEGORIES.items():
        print(f"{num}. {info['name']}")
    
    category_choice = input("\n请选择数据类别 (1-7): ").strip()
    
    try:
        category_num = int(category_choice)
        if 1 <= category_num <= 7:
            selected_category = CATEGORIES[category_num]
            
            # 找到该类别下的所有数据集
            matching_datasets = []
            for i, (dataset_id, config) in enumerate(dataset_manager.datasets.items(), 1):
                if selected_category['pattern'] in config['category']:
                    matching_datasets.append((i, dataset_id, config))
            
            if not matching_datasets:
                print(f"❌ 该类别下没有可用的数据集")
                return None, None, None
            
            print(f"\n✅ {selected_category['name']} 类别下的数据集:")
            print("-" * 60)
            for j, (original_num, dataset_id, config) in enumerate(matching_datasets, 1):
                print(f"{j}. {dataset_id} - {config['name']}")
                print(f"   覆盖率: {config['coverage']:<4} | 字段数: {config['field_count']:<3}")
                print()
            
            dataset_choice = input(f"请选择数据集 (1-{len(matching_datasets)}): ").strip()
            
            try:
                choice_num = int(dataset_choice)
                if 1 <= choice_num <= len(matching_datasets):
                    selected_idx = matching_datasets[choice_num - 1][0]  # 获取原始序号
                    return dataset_manager.get_dataset_by_choice(str(selected_idx))
                else:
                    print("❌ 无效选择")
                    return None, None, None
            except ValueError:
                print("❌ 请输入有效数字")
                return None, None, None
        else:
            print("❌ 无效的类别选择")
            return None, None, None
    except ValueError:
        print("❌ 请输入有效数字")
        return None, None, None

# ==================== Day7新增：运算符组合选择功能 ====================

def choose_combination_mode(combination_manager):
    """选择运算符组合模式（Day7新增功能）"""
    
    print("\n🔧 请选择运算符组合模式:")
    print("="*50)
    print("1. 经典组合模式 - 使用预设的经典运算符组合")
    print("2. 批量组合模式 - 连续测试多个组合（推荐）")
    print("3. 自定义模式 - 手动选择运算符（回退到Day6方式）")
    
    mode_choice = input("\n请选择模式 (1-3): ").strip()
    
    if mode_choice == '1':
        return choose_single_combination(combination_manager)
    elif mode_choice == '2':
        return choose_batch_combinations(combination_manager)
    elif mode_choice == '3':
        return None  # 回退到原有的basic/advanced选择
    else:
        print("❌ 无效选择")
        return None

def choose_single_combination(combination_manager):
    """选择单个经典组合"""
    
    combination_manager.list_all_combinations()
    
    combo_choice = input(f"\n请选择组合 (1-{len(combination_manager.combination_sets)}): ").strip()
    
    combo_id, combo_config = combination_manager.get_combination_by_choice(combo_choice)
    
    if not combo_id:
        print("❌ 无效的组合选择")
        return None
    
    print(f"\n✅ 已选择组合: {combo_config['name']}")
    print(f"   描述: {combo_config['description']}")
    print(f"   分组运算符: {combo_config['group_ops']}")
    print(f"   时间序列运算符: {combo_config['ts_ops']}")
    print(f"   时间周期: {combo_config['periods']}")
    print(f"   分组方法: {combo_config['groups']}")
    
    return [combo_config]  # 返回单个组合的列表

def choose_batch_combinations(combination_manager):
    """选择多个组合进行批量测试"""
    
    print("\n🚀 批量组合模式 - 推荐组合:")
    print("="*60)
    print("1. 快速测试 - 3个经典组合 [趋势跟踪, 均值回归, 动量策略]")
    print("2. 全面测试 - 5个主要组合 [趋势跟踪, 均值回归, 动量策略, 波动性策略, 保守策略]")
    print("3. 完整测试 - 全部7个组合")
    print("4. 自定义选择 - 手动选择要测试的组合")
    
    batch_choice = input("\n请选择批量模式 (1-4): ").strip()
    
    if batch_choice == '1':
        # 快速测试：3个经典组合
        selected_combos = ['trend_following', 'mean_reversion', 'momentum']
    elif batch_choice == '2':
        # 全面测试：5个主要组合
        selected_combos = ['trend_following', 'mean_reversion', 'momentum', 'volatility_based', 'conservative']
    elif batch_choice == '3':
        # 完整测试：全部组合
        selected_combos = list(combination_manager.combination_sets.keys())
    elif batch_choice == '4':
        # 自定义选择
        return choose_custom_batch_combinations(combination_manager)
    else:
        print("❌ 无效选择")
        return None
    
    # 获取选中组合的配置
    batch_configs = []
    for combo_id in selected_combos:
        if combo_id in combination_manager.combination_sets:
            config = combination_manager.combination_sets[combo_id]
            config['id'] = combo_id  # 添加ID用于标识
            batch_configs.append(config)
    
    print(f"\n✅ 已选择 {len(batch_configs)} 个组合进行批量测试:")
    for i, config in enumerate(batch_configs, 1):
        print(f"   {i}. {config['name']} - {config['description'][:40]}...")
    
    return batch_configs

def choose_custom_batch_combinations(combination_manager):
    """自定义选择多个组合"""
    
    combination_manager.list_all_combinations()
    
    print("\n请输入要测试的组合序号，用逗号分隔 (例如: 1,2,5):")
    choices_input = input("组合序号: ").strip()
    
    try:
        choice_nums = [int(x.strip()) for x in choices_input.split(',')]
        combo_ids = list(combination_manager.combination_sets.keys())
        
        selected_configs = []
        for choice_num in choice_nums:
            if 1 <= choice_num <= len(combo_ids):
                combo_id = combo_ids[choice_num - 1]
                config = combination_manager.combination_sets[combo_id]
                config['id'] = combo_id
                selected_configs.append(config)
            else:
                print(f"❌ 无效序号: {choice_num}")
        
        if selected_configs:
            print(f"\n✅ 已选择 {len(selected_configs)} 个组合:")
            for i, config in enumerate(selected_configs, 1):
                print(f"   {i}. {config['name']}")
            return selected_configs
        else:
            print("❌ 没有选择有效的组合")
            return None
            
    except ValueError:
        print("❌ 输入格式错误，请使用数字和逗号")
        return None

def generate_alpha_expressions_with_combination(datafields_list, combination_config):
    """使用指定组合配置生成Alpha表达式（Day7新增）"""
    
    combo_name = combination_config.get('name', '未知组合')
    print(f"\n🔧 使用组合配置生成Alpha表达式: {combo_name}")
    print(f"   分组操作符: {len(combination_config['group_ops'])} 种 - {combination_config['group_ops']}")
    print(f"   时间序列操作符: {len(combination_config['ts_ops'])} 种 - {combination_config['ts_ops']}")
    print(f"   基本面字段: {len(datafields_list)} 个")
    print(f"   时间周期: {combination_config['periods']} 天")
    print(f"   分组方法: {len(combination_config['groups'])} 种 - {combination_config['groups']}")
    
    alpha_expressions = []
    expression_count = 0
    
    total_combinations = (len(combination_config['group_ops']) * 
                        len(combination_config['ts_ops']) * 
                        len(datafields_list) * 
                        len(combination_config['periods']) * 
                        len(combination_config['groups']))
    print(f"   预期生成表达式总数: {total_combinations:,} 个")
    
    for gco in combination_config['group_ops']:
        for tco in combination_config['ts_ops']:
            for cf in datafields_list:
                for d in combination_config['periods']:
                    for grp in combination_config['groups']:
                        alpha_expression = f"{gco}({tco}({cf}, {d}), {grp})"
                        alpha_expressions.append(alpha_expression)
                        expression_count += 1
                        
                        if expression_count % 500 == 0:
                            print(f"   已生成 {expression_count:,} 个表达式...")
    
    print(f"\n✅ 组合 '{combo_name}' Alpha表达式生成完成！")
    print(f"   实际生成表达式总数: {len(alpha_expressions):,} 个")
    print(f"   表达式示例:")
    for i, expr in enumerate(alpha_expressions[:3], 1):
        print(f"     {i}. {expr}")
    
    return alpha_expressions

# ==================== 完全继承Day6的所有其他核心函数 ====================

def check_alpha_quality(alpha_data):
    """检查Alpha是否符合7个核心质量标准（完全继承自day6.py）"""
    is_data = alpha_data.get('is', {})
    
    # 提取关键指标（严格按照API返回的字段名）
    sharpe = is_data.get('sharpe', 0)
    fitness = is_data.get('fitness', 0)
    turnover = is_data.get('turnover', 0) * 100  # 转换为百分比
    sub_universe_sharpe = is_data.get('subUniverseSharpe', 0)  # 注意字段名
    weight_concentration = is_data.get('maxWeight', 0) * 100  # 最大权重作为权重集中度
    ic_mean = is_data.get('ic_mean', 0)
    returns = is_data.get('returns', 0)
    
    # 7个核心质量标准检查（严格按照权威案例）
    checks = {
        # PASS标准 (4个)
        'turnover_above_min': {
            'passed': turnover >= 1.0,
            'message': f"Turnover of {turnover:.2f}% is {'above' if turnover >= 1.0 else 'below'} cutoff of 1%.",
            'status': 'PASS' if turnover >= 1.0 else 'FAIL'
        },
        'turnover_below_max': {
            'passed': turnover <= 70.0,
            'message': f"Turnover of {turnover:.2f}% is {'below' if turnover <= 70.0 else 'above'} cutoff of 70%.",
            'status': 'PASS' if turnover <= 70.0 else 'FAIL'
        },
        'sub_universe_sharpe_ok': {
            'passed': sub_universe_sharpe >= -1.54,
            'message': f"Sub-universe Sharpe of {sub_universe_sharpe:.2f} is {'above' if sub_universe_sharpe >= -1.54 else 'below'} cutoff of -1.54.",
            'status': 'PASS' if sub_universe_sharpe >= -1.54 else 'FAIL'
        },
        'competition_challenge': {
            'passed': True,  # 默认通过竞赛挑战
            'message': "Competition Challenge matches.",
            'status': 'PASS'
        },
        
        # FAIL标准 (3个) - 需要全部通过才算合格
        'sharpe_ok': {
            'passed': sharpe >= 1.25,
            'message': f"Sharpe of {sharpe:.2f} is {'above' if sharpe >= 1.25 else 'below'} cutoff of 1.25.",
            'status': 'PASS' if sharpe >= 1.25 else 'FAIL'
        },
        'fitness_ok': {
            'passed': fitness >= 1.0,
            'message': f"Fitness of {fitness:.2f} is {'above' if fitness >= 1.0 else 'below'} cutoff of 1.",
            'status': 'PASS' if fitness >= 1.0 else 'FAIL'
        },
        'weight_concentration_ok': {
            'passed': weight_concentration <= 10.0,
            'message': f"Weight concentration {weight_concentration:.0f}% is {'above' if weight_concentration <= 10.0 else 'below'} cutoff of 10% on {datetime.now().strftime('%m/%d/%Y')}.",
            'status': 'PASS' if weight_concentration <= 10.0 else 'FAIL'
        }
    }
    
    # 计算通过的检查项数量
    passed_checks = sum(1 for check in checks.values() if check['passed'])
    total_checks = len(checks)
    
    # 需要全部7个检查项都通过才算合格（严格标准）
    is_qualified = passed_checks == 7
    
    result = {
        'qualified': is_qualified,
        'passed_checks': passed_checks,
        'total_checks': total_checks,
        'metrics': {
            'sharpe': sharpe,
            'fitness': fitness,
            'turnover': turnover,
            'sub_universe_sharpe': sub_universe_sharpe,
            'weight_concentration': weight_concentration,
            'ic_mean': ic_mean,
            'returns': returns
        },
        'checks': checks
    }
    
    return is_qualified, result

def save_qualified_alpha(alpha_id, alpha_expression, quality_result, alpha_index=None, combination_name=None):
    """保存合格的Alpha ID到文件（Day7增强版，增加组合名称）"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 准备保存的数据（包含完整的检测结果和序号）
    alpha_record = {
        'alpha_id': alpha_id,
        'expression': alpha_expression,
        'alpha_index': alpha_index,  # 添加序号
        'combination_name': combination_name,  # Day7增加组合名称
        'timestamp': timestamp,
        'qualified': quality_result['qualified'],
        'passed_checks': quality_result['passed_checks'],
        'total_checks': quality_result['total_checks'],
        'metrics': quality_result['metrics'],
        'detailed_checks': quality_result['checks']
    }
    
    # 追加到合格 Alpha文件
    with open(QUALIFIED_ALPHA_FILE, 'a', encoding='utf-8') as f:
        f.write(json.dumps(alpha_record, ensure_ascii=False) + '\n')
    
    # 同时保存到专门的成功Alpha ID文件（包含序号）
    success_alpha_file = 'successful_alpha_ids.txt'
    
    # 如果文件不存在，先写入表头
    if not os.path.exists(success_alpha_file):
        with open(success_alpha_file, 'w', encoding='utf-8') as f:
            f.write("Alpha_ID\t序号\tAlpha表达式\t组合名称\t时间戳\t通过标准\n")
    
    with open(success_alpha_file, 'a', encoding='utf-8') as f:
        index_str = f"第{alpha_index}个" if alpha_index else "未知序号"
        combo_str = combination_name if combination_name else "未知组合"
        f.write(f"{alpha_id}\t{index_str}\t{alpha_expression}\t{combo_str}\t{timestamp}\t{quality_result['passed_checks']}/{quality_result['total_checks']}\n")
    
    print(f"💾 已保存合格Alpha: {alpha_id} ({index_str})")
    if combination_name:
        print(f"   组合名称: {combination_name}")
    print(f"   详细记录: {QUALIFIED_ALPHA_FILE}")
    print(f"   ID列表: {success_alpha_file}")
    
    # 特别提醒：这是完全通过的Alpha
    if quality_result['passed_checks'] == 7:
        print(f"🏆 重要发现！Alpha {alpha_id} 完全通过所有7个标准！")
        print(f"   这是第 {alpha_index} 个测试的Alpha，请重点关注！")

def display_alpha_result(alpha_id, alpha_expression, quality_result, combination_name=None):
    """显示Alpha测试结果（Day7增强版，增加组合信息）"""
    print(f"\n" + "="*60)
    print(f"📄 Alpha测试结果 - ID: {alpha_id}")
    if combination_name:
        print(f"🔧 组合名称: {combination_name}")
    print(f"="*60)
    print(f"表达式: {alpha_expression}")
    print(f"质量评级: {'✅ 合格' if quality_result['qualified'] else '❌ 不合格'}")
    print(f"通过检查: {quality_result['passed_checks']}/{quality_result['total_checks']}")
    
    print(f"\n📋 IS Testing Status (IS 测试状态)")
    
    # 统计PASS和FAIL数量
    pass_count = sum(1 for check in quality_result['checks'].values() if check['status'] == 'PASS')
    fail_count = sum(1 for check in quality_result['checks'].values() if check['status'] == 'FAIL')
    
    print(f"\n✅ {pass_count} PASS ({pass_count} 及格)")
    for check_name, check_info in quality_result['checks'].items():
        if check_info['status'] == 'PASS':
            print(f"   {check_info['message']}")
    
    if fail_count > 0:
        print(f"\n❌ {fail_count} FAIL ({fail_count} 不及格)")
        for check_name, check_info in quality_result['checks'].items():
            if check_info['status'] == 'FAIL':
                print(f"   {check_info['message']}")
    
    print(f"\n📈 详细性能指标:")
    metrics = quality_result['metrics']
    print(f"   Sharpe: {metrics['sharpe']:.2f}")
    print(f"   Fitness: {metrics['fitness']:.2f}")
    print(f"   Turnover: {metrics['turnover']:.2f}%")
    print(f"   Sub-universe Sharpe: {metrics['sub_universe_sharpe']:.2f}")
    print(f"   Weight Concentration: {metrics['weight_concentration']:.0f}%")
    print(f"   IC Mean: {metrics['ic_mean']:.4f}")
    print(f"   Returns: {metrics['returns']:.4f}")
    print(f"\n" + "="*60)

def view_qualified_alphas():
    """查看已保存的合格Alpha策略（完全继承自day6.py）"""
    if not os.path.exists(QUALIFIED_ALPHA_FILE):
        print("📝 暂无保存的合格Alpha策略")
        return
    
    print("\n📋 已保存的合格Alpha策略:")
    print("="*60)
    
    try:
        with open(QUALIFIED_ALPHA_FILE, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if not lines:
            print("📝 暂无保存的合格Alpha策略")
            return
        
        for i, line in enumerate(lines, 1):
            try:
                alpha_record = json.loads(line.strip())
                print(f"\n{i}. Alpha ID: {alpha_record['alpha_id']}")
                print(f"   表达式: {alpha_record['expression']}")
                if 'combination_name' in alpha_record and alpha_record['combination_name']:
                    print(f"   组合名称: {alpha_record['combination_name']}")
                print(f"   保存时间: {alpha_record['timestamp']}")
                print(f"   通过检查: {alpha_record['passed_checks']}/{alpha_record['total_checks']}")
                
                metrics = alpha_record['metrics']
                print(f"   夏普比率: {metrics['sharpe']:.3f} | 适应度: {metrics['fitness']:.3f}")
                
            except json.JSONDecodeError:
                print(f"   ❌ 第{i}行数据格式错误")
                
    except Exception as e:
        print(f"❌ 读取文件时发生错误: {str(e)}")

def setup_logging():
    """配置日志系统（完全继承自day6.py）"""
    logging.basicConfig(
        filename=SIMULATION_LOG_FILE,
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        filemode='a'
    )
    
    # 创建一个自定义的日志记录器，用于记录详细的Alpha测试结果
    detailed_logger = logging.getLogger('alpha_details')
    detailed_handler = logging.FileHandler('alpha_test_results.log', mode='a', encoding='utf-8')
    detailed_formatter = logging.Formatter('%(asctime)s - %(message)s')
    detailed_handler.setFormatter(detailed_formatter)
    detailed_logger.addHandler(detailed_handler)
    detailed_logger.setLevel(logging.INFO)
    
    return detailed_logger

def log_alpha_result(alpha_id, alpha_expression, quality_result, detailed_logger, combination_name=None):
    """记录Alpha测试结果到详细日志文件（Day7增强版）"""
    log_content = f"""
{'='*60}
Alpha测试结果 - ID: {alpha_id}
{'='*60}
表达式: {alpha_expression}"""
    
    if combination_name:
        log_content += f"\n组合名称: {combination_name}"
    
    log_content += f"""
质量评级: {'合格' if quality_result['qualified'] else '不合格'}
通过检查: {quality_result['passed_checks']}/{quality_result['total_checks']}

IS Testing Status (IS 测试状态)
"""
    
    # 统计PASS和FAIL数量
    pass_count = sum(1 for check in quality_result['checks'].values() if check['status'] == 'PASS')
    fail_count = sum(1 for check in quality_result['checks'].values() if check['status'] == 'FAIL')
    
    log_content += f"\n{pass_count} PASS ({pass_count} 及格)\n"
    for check_name, check_info in quality_result['checks'].items():
        if check_info['status'] == 'PASS':
            log_content += f"   {check_info['message']}\n"
    
    if fail_count > 0:
        log_content += f"\n{fail_count} FAIL ({fail_count} 不及格)\n"
        for check_name, check_info in quality_result['checks'].items():
            if check_info['status'] == 'FAIL':
                log_content += f"   {check_info['message']}\n"
    
    log_content += f"\n详细性能指标:\n"
    metrics = quality_result['metrics']
    log_content += f"   Sharpe: {metrics['sharpe']:.2f}\n"
    log_content += f"   Fitness: {metrics['fitness']:.2f}\n"
    log_content += f"   Turnover: {metrics['turnover']:.2f}%\n"
    log_content += f"   Sub-universe Sharpe: {metrics['sub_universe_sharpe']:.2f}\n"
    log_content += f"   Weight Concentration: {metrics['weight_concentration']:.0f}%\n"
    log_content += f"   IC Mean: {metrics['ic_mean']:.4f}\n"
    log_content += f"   Returns: {metrics['returns']:.4f}\n"
    log_content += f"{'='*60}\n"
    
    detailed_logger.info(log_content)

def create_alpha_list(alpha_expressions, searchScope):
    """
    将Alpha表达式封装为WorldQuant Brain模拟请求格式（完全继承自day6.py）
    """
    print("\n🎯 开始Alpha策略封装和模拟准备阶段")
    print("="*60)
    print("\n📦 将Alpha表达式封装为WorldQuant Brain模拟请求格式...")
    
    alpha_list = []
    
    for index, alpha_expression in enumerate(alpha_expressions, start=1):
        if index <= 5:  # 只显示前5个的详细信息
            print(f"📝 正在处理第 {index} 个Alpha表达式...")
            print(f"   表达式: {alpha_expression}")
        
        simulation_data = {
            "type": "REGULAR",
            "settings": {
                "instrumentType": searchScope['instrumentType'],
                "region": searchScope['region'],
                "universe": searchScope['universe'],
                "delay": int(searchScope['delay']),
                "decay": 0,
                "neutralization": "SUBINDUSTRY",
                "truncation": 0.01,
                "pasteurization": "ON",
                "unitHandling": "VERIFY",
                "nanHandling": "OFF",
                "language": "FASTEXPR",
                "visualization": False,
            },
            "regular": alpha_expression
        }
        
        alpha_list.append(simulation_data)
        
        if index % 100 == 0 or index <= 5:
            print(f"   ✅ 已封装 {len(alpha_list)} 个Alpha策略")
    
    print(f"\n📄 Alpha策略封装完成统计：")
    print(f"   总策略数量: {len(alpha_list):,} 个")
    print(f"   封装格式: WorldQuant Brain标准模拟请求")
    print(f"   目标市场: {searchScope['region']} {searchScope['universe']}")
    print(f"   中性化方法: 子行业中性")
    
    if alpha_list:
        print(f"\n🔍 封装示例 (第1个Alpha策略):")
        print(f"   Alpha表达式: {alpha_list[0]['regular']}")
    
    return alpha_list

# ==================== Day7主程序 ====================

def check_session_validity(sess):
    """检查会话有效性（Day7新增功能）"""
    try:
        # 发送一个简单的认证检查请求
        test_response = sess.get('https://api.worldquantbrain.com/data-fields?limit=1')
        if test_response.status_code in [401, 403]:
            return False
        return True
    except Exception:
        return False

def proactive_session_refresh(sess, alpha_index, total_processed):
    """
    主动会话刷新机制（Day7新增功能）
    
    在以下情况下主动刷新认证：
    1. 每处理50个Alpha后检查一次
    2. 每30分钟检查一次（基于处理速度估算）
    3. 检测到会话可能失效时
    
    参数：
    - sess: 当前会话对象
    - alpha_index: 当前Alpha序号
    - total_processed: 已处理总数
    
    返回：
    - 刷新后的会话对象（可能是新的）
    """
    need_refresh = False
    refresh_reason = ""
    
    # 检查条件1：每50个Alpha检查一次
    if alpha_index % 50 == 0 and alpha_index > 0:
        need_refresh = True
        refresh_reason = f"达到检查间隔（第{alpha_index}个Alpha）"
    
    # 检查条件2：每30分钟强制刷新（估算处理速度：约1个Alpha/分钟）
    elif alpha_index % 30 == 0 and alpha_index > 0:
        need_refresh = True
        refresh_reason = f"定时刷新（约{alpha_index}分钟后）"
    
    if need_refresh:
        print(f"\n🔄 主动会话刷新：{refresh_reason}")
        print(f"   检查当前会话有效性...")
        
        if not check_session_validity(sess):
            print(f"   ⚠️ 检测到会话已失效，执行重新认证")
            try:
                new_sess = sign_in()
                print(f"   ✅ 会话重新认证成功")
                return new_sess
            except Exception as e:
                print(f"   ❌ 重新认证失败: {str(e)}")
                return sess
        else:
            print(f"   ✅ 当前会话仍然有效")
    
    return sess

def batch_submit_alphas_with_combination(sess, alpha_list, combination_name=None, test_mode=True):
    """
    批量提交Alpha策略（Day7增强版，增加组合名称跟踪）
    
    参数说明：
    - sess: 已认证的会话对象
    - alpha_list: Alpha策略列表
    - combination_name: 组合名称（用于日志和保存）
    - test_mode: 是否为测试模式（True=只处理前5个，False=处理全部）
    
    返回值：
    - tuple: (成功数量, 失败数量, 合格Alpha列表)
    """
    combo_display = f"🔧 [{combination_name}]" if combination_name else ""
    print(f"\n🚀 开始Alpha策略批量提交和模拟执行 {combo_display}")
    print("="*60)
    print("\n⚙️ 模拟执行配置：")
    print(f"   - 处理策略数量: 全部 {len(alpha_list):,} 个Alpha策略")
    if combination_name:
        print(f"   - 组合名称: {combination_name}")
    print("   - 最大重试次数: 15次")
    print("   - 自动重连机制: 启用")
    print("   - 错误处理: 全面覆盖")
    print("   - 日志记录: 启用")
    
    # Alpha提交失败容忍度配置
    alpha_fail_attempt_tolerance = 15
    
    # 初始化提交统计计数器
    total_processed = 0
    successful_submissions = 0
    failed_submissions = 0
    qualified_alphas = []
    
    # 根据模式决定处理数量
    if test_mode:
        max_test_count = min(5, len(alpha_list))  # 测试模式：最多测试5个Alpha
        print(f"\n🧪 测试模式：只处理前 {max_test_count} 个Alpha进行质量检测")
        alpha_subset = alpha_list[:max_test_count]
    else:
        max_test_count = len(alpha_list)  # 生产模式：处理全部Alpha
        print(f"\n🚀 生产模式：处理全部 {max_test_count} 个Alpha进行质量检测")
        alpha_subset = alpha_list
    
    for alpha_index, alpha in enumerate(alpha_subset, start=1):
        total_processed += 1
        
        # Day7新增：主动会话刷新机制（适用于长时间运行的生产模式）
        if not test_mode:  # 只在生产模式下启用主动刷新
            sess = proactive_session_refresh(sess, alpha_index, total_processed)
        
        print(f"\n" + "-"*50)
        print(f"📤 正在处理第 {alpha_index} 个Alpha策略 {combo_display}")
        print(f"   Alpha表达式: {alpha['regular']}")
        
        # 记录开始处理的日志
        log_message = f"开始处理Alpha {alpha_index}: {alpha['regular']}"
        if combination_name:
            log_message += f" (组合: {combination_name})"
        logging.info(log_message)
        
        # 智能重试机制
        keep_trying = True
        failure_count = 0
        submission_successful = False
        alpha_id = None
        
        # 实施智能重试循环：处理网络问题、认证失效、服务器错误等情况
        while keep_trying:
            try:
                print(f"\n🔄 尝试提交 (第 {failure_count + 1} 次)...")
                
                # 向WorldQuant Brain API发送Alpha模拟请求
                sim_resp = sess.post(
                    'https://api.worldquantbrain.com/simulations',
                    json=alpha,
                    timeout=30
                )
                
                # 检查响应状态码，201表示成功创建模拟
                if sim_resp.status_code == 201:
                    sim_progress_url = sim_resp.headers.get('Location', 'URL not found')
                    
                    success_message = f'✅ Alpha {alpha_index} 提交成功！Location: {sim_progress_url}'
                    logging.info(success_message)
                    print(success_message)
                    print(f"   状态码: {sim_resp.status_code}")
                    
                    # 等待模拟完成并获取Alpha ID
                    try:
                        start_time = datetime.now()
                        print(f"   ⏳ 等待模拟完成...")
                        
                        while True:
                            sim_progress_resp = sess.get(sim_progress_url)
                            retry_after_sec = float(sim_progress_resp.headers.get("Retry-After", 0))
                            
                            if retry_after_sec == 0:  # simulation done!
                                # 检查响应状态码
                                if sim_progress_resp.status_code == 200:
                                    response_data = sim_progress_resp.json()
                                    print(f"   📋 模拟完成响应: {response_data}")
                                    
                                    # 尝试从不同字段获取Alpha ID
                                    alpha_id = None
                                    if 'alpha' in response_data:
                                        alpha_id = response_data['alpha']
                                    elif 'alphaId' in response_data:
                                        alpha_id = response_data['alphaId']
                                    elif 'id' in response_data:
                                        alpha_id = response_data['id']
                                    else:
                                        # 从URL中提取ID作为备用方案
                                        alpha_id = sim_progress_url.split('/')[-1] if '/' in sim_progress_url else 'Unknown'
                                        print(f"   ⚠️ 无法从响应中找到Alpha ID，使用URL ID: {alpha_id}")
                                    
                                    print(f"   ✅ 获得 Alpha ID: {alpha_id}")
                                    break
                                else:
                                    print(f"   ❌ 模拟完成但状态码异常: {sim_progress_resp.status_code}")
                                    alpha_id = sim_progress_url.split('/')[-1] if '/' in sim_progress_url else 'Unknown'
                                    break
                            
                            elapsed = (datetime.now() - start_time).total_seconds()
                            progress = min(95, (elapsed / 30) * 100)
                            print(f"   ⏳ 等待模拟结果... ({elapsed:.1f} 秒 | 进度约 {progress:.0f}%)")
                            sleep(retry_after_sec)
                            
                            if elapsed > 120:  # 最多等待2分钟
                                print(f"   ⚠️ 模拟超时，但Alpha已提交")
                                alpha_id = sim_progress_url.split('/')[-1] if '/' in sim_progress_url else 'Unknown'
                                break
                        
                        successful_submissions += 1
                        submission_successful = True
                        keep_trying = False
                        
                    except Exception as e:
                        print(f"   ⚠️ 获取Alpha ID时出错: {str(e)}，但Alpha已成功提交")
                        alpha_id = sim_progress_url.split('/')[-1] if '/' in sim_progress_url else 'Unknown'
                        successful_submissions += 1
                        submission_successful = True
                        keep_trying = False
                    
                else:
                    # 失败响应处理
                    error_message = f"❌ Alpha {alpha_index} 提交失败，状态码: {sim_resp.status_code}"
                    print(error_message)
                    print(f"   响应内容: {sim_resp.text[:200]}...")
                    logging.error(f"{error_message}, 响应: {sim_resp.text}")
                    
                    # Day7增强：更智能的认证问题处理
                    if sim_resp.status_code in [401, 403]:
                        print("🔐 检测到认证问题，执行重新登录...")
                        try:
                            sess = sign_in()
                            print("✅ 重新认证成功，继续处理")
                            # 重置失败计数，给新会话一个机会
                            if failure_count > 3:
                                failure_count = max(1, failure_count - 2)  # 适度减少失败计数
                                print(f"   ℹ️ 重新认证后调整失败计数为 {failure_count}")
                        except Exception as auth_error:
                            print(f"   ❌ 重新认证失败: {str(auth_error)}")
                            print(f"   ⚠️ 将使用原会话继续尝试")
                    elif sim_resp.status_code >= 500:
                        print("🔧 服务器错误，这通常是临时性问题")
                    elif sim_resp.status_code == 429:
                        print("🚫 请求过于频繁，稍后重试")
                        # 对于限流错误，增加额外等待时间
                        additional_wait = min(30, 5 * (failure_count + 1))
                        print(f"   额外等待 {additional_wait} 秒后重试")
                        sleep(additional_wait)
                    
                    failure_count += 1
                    
            except Exception as e:
                # 通用异常处理（Day7增强版）
                general_error = f"🚨 Alpha {alpha_index} 发生未预期异常: {type(e).__name__}: {str(e)}"
                logging.error(general_error)
                print(general_error)
                
                # 检查是否为网络或连接相关错误
                if any(keyword in str(e).lower() for keyword in ['connection', 'timeout', 'network', 'ssl']):
                    print("   🔄 检测到网络问题，尝试重新建立会话...")
                    try:
                        sess = sign_in()
                        print("   ✅ 会话重新建立成功")
                        # 适度减少失败计数，给新会话机会
                        if failure_count > 2:
                            failure_count = max(1, failure_count - 1)
                            print(f"   ℹ️ 网络重连后调整失败计数为 {failure_count}")
                    except Exception as reconnect_error:
                        print(f"   ❌ 重新建立会话失败: {str(reconnect_error)}")
                else:
                    print("   尝试继续使用当前会话...")
                
                failure_count += 1
            
            # 重试逻辑和失败处理（Day7增强版）
            if failure_count >= alpha_fail_attempt_tolerance:
                final_error = f"💔 Alpha {alpha_index} 达到最大重试次数 ({alpha_fail_attempt_tolerance})，放弃处理"
                logging.error(final_error)
                print(final_error)
                print("   🔄 最后尝试：重新登录以处理下一个Alpha...")
                try:
                    sess = sign_in()
                    print("   ✅ 为下一个Alpha准备的新会话已就绪")
                except Exception as final_auth_error:
                    print(f"   ❌ 最后的重新认证也失败: {str(final_auth_error)}")
                    print(f"   ⚠️ 将使用当前会话继续处理后续任务")
                
                failure_count = 0  # 重置计数器
                failed_submissions += 1
                keep_trying = False
                break
            
            if keep_trying and not submission_successful:
                wait_time = min(2 ** (failure_count - 1), 15)
                print(f"   等待 {wait_time} 秒后进行第 {failure_count + 1} 次重试...")
                sleep(wait_time)
        
        # 单个Alpha处理完成
        completion_message = f"📊 Alpha {alpha_index} 处理完成"
        if submission_successful:
            completion_message += " - ✅ 成功"
            print(completion_message)
            logging.info(completion_message)
            
            # 立即进行质量检测
            print(f"\n🔍 开始对Alpha {alpha_id} 进行7个标准质量检测...")
            
            try:
                sleep(5)  # 等待一下让指标计算完成
                
                alpha_url = f"https://api.worldquantbrain.com/alphas/{alpha_id}"
                alpha_detail = sess.get(alpha_url)
                
                # Day7新增：检查获取Alpha详情时的认证问题
                if alpha_detail.status_code in [401, 403]:
                    print(f"   🔐 检测到认证问题，重新登录后重试...")
                    try:
                        sess = sign_in()
                        alpha_detail = sess.get(alpha_url)  # 重新尝试
                        print(f"   ✅ 重新认证后重试成功")
                    except Exception as detail_auth_error:
                        print(f"   ❌ 重新认证失败: {str(detail_auth_error)}")
                
                if alpha_detail.status_code == 200:
                    alpha_data = alpha_detail.json()
                    
                    if 'is' not in alpha_data:
                        print(f"❌ Alpha {alpha_id} 无法获取指标数据，可能还在计算中")
                        print(f"   建议稍后手动检查Alpha ID: {alpha_id}")
                    else:
                        print(f"✅ 获取到Alpha {alpha_id} 的性能指标")
                        
                        # 进行7个标准的质量检测
                        is_qualified, quality_result = check_alpha_quality(alpha_data)
                        
                        # 显示测试结果
                        display_alpha_result(alpha_id, alpha['regular'], quality_result, combination_name)
                        
                        # 记录到详细日志
                        detailed_logger = logging.getLogger('alpha_details')
                        log_alpha_result(alpha_id, alpha['regular'], quality_result, detailed_logger, combination_name)
                        
                        # 如果合格，保存到文件
                        if is_qualified:
                            save_qualified_alpha(alpha_id, alpha['regular'], quality_result, alpha_index, combination_name)
                            print(f"🎉 Alpha {alpha_id} 通过质量检测并已保存！")
                            qualified_alphas.append({
                                'alpha_id': alpha_id,
                                'expression': alpha['regular'],
                                'index': alpha_index,
                                'combination_name': combination_name,
                                'qualified': True
                            })
                        else:
                            print(f"❌ Alpha {alpha_id} 未通过质量检测")
                            qualified_alphas.append({
                                'alpha_id': alpha_id,
                                'expression': alpha['regular'],
                                'index': alpha_index,
                                'combination_name': combination_name,
                                'qualified': False
                            })
                        
                        log_status = f"Alpha {alpha_id}: {'合格' if is_qualified else '不合格'} - {quality_result['passed_checks']}/{quality_result['total_checks']} 通过"
                        if combination_name:
                            log_status += f" (组合: {combination_name})"
                        logging.info(log_status)
                        
                else:
                    print(f"❌ 无法获取Alpha {alpha_id} 的详细结果，状态码: {alpha_detail.status_code}")
                    
            except Exception as e:
                error_msg = f"❌ 检测Alpha {alpha_id} 时发生错误: {str(e)}"
                if combination_name:
                    error_msg += f" (组合: {combination_name})"
                print(error_msg)
                logging.error(error_msg)
        else:
            completion_message += " - ❌ 失败"
            print(completion_message)
            logging.info(completion_message)
        
        # 显示当前处理进度
        print(f"\n📊 当前进度: {total_processed}/{max_test_count}")
        print(f"   成功: {successful_submissions}, 失败: {failed_submissions}")
        if qualified_alphas:
            qualified_count = sum(1 for alpha in qualified_alphas if alpha.get('qualified', False))
            print(f"   合格: {qualified_count}")
    
    # 批量提交总结报告
    print(f"\n" + "="*60)
    print(f"🏁 Alpha策略批量提交完成 {combo_display}")
    print(f"="*60)
    
    print(f"\n📈 提交统计报告：")
    print(f"   总处理数量: {total_processed} 个Alpha策略")
    print(f"   成功提交: {successful_submissions} 个 ({successful_submissions/total_processed*100:.1f}%)")
    print(f"   提交失败: {failed_submissions} 个 ({failed_submissions/total_processed*100:.1f}%)")
    
    if total_processed > 0:
        success_rate = successful_submissions / total_processed * 100
        print(f"   整体成功率: {success_rate:.1f}%")
    
    final_log = f"批量提交完成 - 总数:{total_processed}, 成功:{successful_submissions}, 失败:{failed_submissions}"
    if combination_name:
        final_log += f" (组合: {combination_name})"
    logging.info(final_log)
    
    return successful_submissions, failed_submissions, qualified_alphas

def maintain_session_health(sess, context="unknown"):
    """
    维护会话健康状态（Day7新增功能）
    
    在关键操作之前检查和维护会话健康
    
    参数：
    - sess: 当前会话对象
    - context: 操作上下文（用于日志）
    
    返回：
    - 健康的会话对象（可能是新的）
    """
    try:
        if not check_session_validity(sess):
            print(f"\n🔴 在{context}操作中检测到会话已失效")
            print(f"   🔄 正在重新建立会话...")
            new_sess = sign_in()
            print(f"   ✅ 会话已重新建立，可以继续{context}操作")
            return new_sess
        return sess
    except Exception as e:
        print(f"   ⚠️ 维护会话健康时发生错误: {str(e)}")
        return sess

def show_main_menu_v7():
    """显示Day7主菜单（保持day6的三个基础模式）"""
    print("\n" + "="*60)
    print("🚀 WorldQuant Brain Alpha策略智能生成系统 (Day7)")
    print("🔧 新增：运算符组合配置化功能")
    print("="*60)
    print("\n📋 请选择运行模式:")
    print("1. 自动模式 - 生成、测试并提交Alpha策略")
    print("2. 提交模式 - 查看已保存的合格Alpha策略")
    print("3. 退出系统")

def main():
    """主程序入口（Day7增强版）"""
    try:
        # 完全继承day6的初始化逻辑，增加组合管理器
        dataset_manager = DatasetManager()
        combination_manager = CombinationManager()  # Day7新增
        detailed_logger = setup_logging()
        
        print("🔐 正在进行API认证...")
        sess = sign_in()
        
        while True:
            show_main_menu_v7()
            
            try:
                choice = input("\n请选择模式 (1-3): ").strip()
                
                if choice == '3':
                    print("👋 感谢使用Day7系统，再见！")
                    break
                    
                elif choice == '2':
                    # 完全继承day6的提交模式
                    view_qualified_alphas()
                    input("\n按回车键继续...")
                    continue
                    
                elif choice == '1':
                    # 自动模式：Day7新增组合功能
                    # 1. 先选择数据集（继承Day6）
                    selected_dataset = choose_by_category(dataset_manager)
                    
                    dataset_id, search_scope, config = selected_dataset
                    
                    if not dataset_id:
                        print("❌ 无效的数据集选择")
                        continue
                    
                    print(f"\n✅ 已选择数据集: {config['name']}")
                    print(f"   配置: {search_scope}")
                    
                    # 2. 选择运算符组合模式（Day7新增）
                    selected_combinations = choose_combination_mode(combination_manager)
                    
                    if not selected_combinations:
                        # 回退到传统方式（Day6风格）
                        print("\n🔧 使用传统运算符选择模式:")
                        print("1. 基础模式 - 3种分组运算符 × 3种时间序列运算符")
                        print("2. 高级模式 - 6种分组运算符 × 6种时间序列运算符")
                        
                        complexity_choice = input("请选择复杂度 (1-2): ").strip()
                        operator_set = 'advanced' if complexity_choice == '2' else 'basic'
                        
                        print(f"\n🎯 开始Alpha策略数据准备阶段")
                        print(f"="*60)
                        
                        # 获取数据字段（Day7新增会话检查）
                        sess = maintain_session_health(sess, "传统模式数据获取")
                        datafields_df = get_datafields(sess, search_scope, dataset_id)
                        
                        if datafields_df.empty:
                            print("❌ 未获取到数据字段，请检查网络连接")
                            continue
                        
                        # 过滤MATRIX类型字段
                        matrix_fields = datafields_df[datafields_df['type'] == "MATRIX"]
                        if matrix_fields.empty:
                            print("❌ 未找到MATRIX类型的数据字段")
                            continue
                        
                        datafields_list = matrix_fields['id'].values
                        print(f"\n✅ 数据字段筛选完成：")
                        print(f"   总MATRIX字段数: {len(datafields_list)} 个")
                        print(f"   示例字段: {list(datafields_list[:5])}")
                        
                        # 使用传统方式生成（复用day6逻辑）
                        op_lib = OperatorLibrary()
                        ops = op_lib.get_operator_set(operator_set)
                        
                        print(f"\n🔧 配置Alpha策略构建参数 ({operator_set}模式)...")
                        print(f"   分组操作符: {len(ops['group_ops'])} 种 - {ops['group_ops']}")
                        print(f"   时间序列操作符: {len(ops['ts_ops'])} 种 - {ops['ts_ops']}")
                        print(f"   基本面字段: {len(datafields_list)} 个")
                        print(f"   时间周期: {ops['periods']} 天")
                        print(f"   分组方法: {len(ops['groups'])} 种 - {ops['groups']}")
                        
                        alpha_expressions = []
                        expression_count = 0
                        
                        total_combinations = len(ops['group_ops']) * len(ops['ts_ops']) * len(datafields_list) * len(ops['periods']) * len(ops['groups'])
                        print(f"   预期生成表达式总数: {total_combinations:,} 个")
                        
                        for gco in ops['group_ops']:
                            for tco in ops['ts_ops']:
                                for cf in datafields_list:
                                    for d in ops['periods']:
                                        for grp in ops['groups']:
                                            alpha_expression = f"{gco}({tco}({cf}, {d}), {grp})"
                                            alpha_expressions.append(alpha_expression)
                                            expression_count += 1
                                            
                                            if expression_count % 1000 == 0:
                                                print(f"   已生成 {expression_count:,} 个表达式...")
                        
                        print(f"\n✅ Alpha表达式生成完成！")
                        print(f"   实际生成表达式总数: {len(alpha_expressions):,} 个")
                        print(f"   表达式示例:")
                        for i, expr in enumerate(alpha_expressions[:5], 1):
                            print(f"     {i}. {expr}")
                        
                        # 询问用户处理模式
                        print("\n⚙️ 请选择处理模式:")
                        print("1. 测试模式 - 只处理前5个Alpha进行质量检测（推荐）")
                        print("2. 生产模式 - 处理全部Alpha策略（可能需要很长时间）")
                        
                        processing_choice = input("请选择模式 (1-2): ").strip()
                        test_mode = processing_choice != '2'  # 默认测试模式，只有明确选择2才是生产模式
                        
                        if not test_mode:
                            confirm = input(f"\n⚠️ 您选择了生产模式，将处理全部 {len(alpha_expressions):,} 个Alpha策略。\n   这可能需要数小时时间，确认继续？(y/N): ").strip().lower()
                            if confirm != 'y':
                                print("已取消，回到测试模式")
                                test_mode = True
                        
                        # 封装Alpha策略
                        alpha_list = create_alpha_list(alpha_expressions, search_scope)
                        
                        # 批量提交并进行质量检测
                        print("\n🚀 传统模式：开始批量提交并测试Alpha策略")
                        successful, failed, qualified_alphas = batch_submit_alphas_with_combination(sess, alpha_list, "传统模式", test_mode)
                        
                    else:
                        # Day7组合模式
                        print(f"\n🎯 开始Alpha策略数据准备阶段")
                        print(f"="*60)
                        
                        # 获取数据字段（Day7新增会话检查）
                        sess = maintain_session_health(sess, "组合模式数据获取")
                        datafields_df = get_datafields(sess, search_scope, dataset_id)
                        
                        if datafields_df.empty:
                            print("❌ 未获取到数据字段，请检查网络连接")
                            continue
                        
                        # 过滤MATRIX类型字段
                        matrix_fields = datafields_df[datafields_df['type'] == "MATRIX"]
                        if matrix_fields.empty:
                            print("❌ 未找到MATRIX类型的数据字段")
                            continue
                        
                        datafields_list = matrix_fields['id'].values
                        print(f"\n✅ 数据字段筛选完成：")
                        print(f"   总MATRIX字段数: {len(datafields_list)} 个")
                        print(f"   示例字段: {list(datafields_list[:5])}")
                        
                        # 询问用户处理模式
                        print("\n⚙️ 请选择处理模式:")
                        print("1. 测试模式 - 每个组合只处理前5个Alpha（推荐）")
                        print("2. 生产模式 - 每个组合处理全部Alpha策略（时间很长）")
                        
                        processing_choice = input("请选择模式 (1-2): ").strip()
                        test_mode = processing_choice != '2'  # 默认测试模式
                        
                        if not test_mode:
                            # 计算总的Alpha数量
                            total_alphas = 0
                            for combo_config in selected_combinations:
                                combo_total = (len(combo_config['group_ops']) * 
                                             len(combo_config['ts_ops']) * 
                                             len(datafields_list) * 
                                             len(combo_config['periods']) * 
                                             len(combo_config['groups']))
                                total_alphas += combo_total
                            
                            confirm = input(f"\n⚠️ 您选择了生产模式，将处理约 {total_alphas:,} 个Alpha策略。\n   这可能需要数小时时间，确认继续？(y/N): ").strip().lower()
                            if confirm != 'y':
                                print("已取消，回到测试模式")
                                test_mode = True
                        
                        # 根据选择的组合进行批量测试
                        all_successful = 0
                        all_failed = 0
                        all_qualified = []
                        
                        for i, combination_config in enumerate(selected_combinations, 1):
                            combo_name = combination_config.get('name', f'组合{i}')
                            combo_id = combination_config.get('id', f'combo_{i}')
                            
                            print(f"\n" + "="*60)
                            print(f"🔧 正在测试组合 {i}/{len(selected_combinations)}: {combo_name}")
                            print(f"="*60)
                            
                            # 使用组合配置生成Alpha表达式
                            alpha_expressions = generate_alpha_expressions_with_combination(datafields_list, combination_config)
                            
                            # 封装Alpha策略
                            alpha_list = create_alpha_list(alpha_expressions, search_scope)
                            
                            # 批量提交并进行质量检测
                            successful, failed, qualified_alphas = batch_submit_alphas_with_combination(sess, alpha_list, combo_name, test_mode)
                            
                            # 统计累计结果
                            all_successful += successful
                            all_failed += failed
                            all_qualified.extend(qualified_alphas)
                            
                            # 组合测试结果总结
                            if qualified_alphas:
                                combo_qualified = sum(1 for alpha in qualified_alphas if alpha.get('qualified', False))
                                print(f"\n📊 组合 '{combo_name}' 测试结果:")
                                print(f"   提交数量: {successful} 个")
                                print(f"   合格数量: {combo_qualified} 个")
                                if successful > 0:
                                    print(f"   合格率: {combo_qualified/successful*100:.1f}%")
                            
                        # 显示最终统计（完全复用day6的逻辑）
                        if all_qualified:
                            truly_qualified = sum(1 for alpha in all_qualified if alpha.get('qualified', False))
                            
                            print(f"\n" + "="*60)
                            print(f"🏁 Day7组合模式 Alpha策略质量检测完成")
                            print(f"="*60)
                            print(f"📈 最终统计报告：")
                            print(f"   测试组合数量: {len(selected_combinations)} 个")
                            print(f"   总提交数量: {all_successful} 个Alpha策略")
                            print(f"   质量检测数量: {len(all_qualified)} 个")
                            print(f"   真正合格数量: {truly_qualified} 个")
                            if len(all_qualified) > 0:
                                print(f"   整体合格率: {truly_qualified/len(all_qualified)*100:.1f}%")
                            print(f"\n📁 文件输出：")
                            print(f"   合格Alpha详情: {QUALIFIED_ALPHA_FILE}")
                            print(f"   成功Alpha ID列表: successful_alpha_ids.txt")
                            print(f"   详细测试日志: alpha_test_results.log")
                            print(f"   基础日志: {SIMULATION_LOG_FILE}")
                        else:
                            print(f"\n❌ 没有成功提交的Alpha策略")
                    
                    input("\n按回车键继续...")
                    
                else:
                    print("❌ 无效选择，请输入1-3之间的数字")
                    
            except KeyboardInterrupt:
                print("\n\n👋 用户中断，退出程序")
                break
            except Exception as e:
                print(f"❌ 程序运行出错: {str(e)}")
                logging.error(f"程序运行出错: {str(e)}")
                input("\n按回车键继续...")
                
    except Exception as e:
        print(f"❌ 程序初始化失败: {str(e)}")
        logging.error(f"程序初始化失败: {str(e)}")

if __name__ == "__main__":
    main()