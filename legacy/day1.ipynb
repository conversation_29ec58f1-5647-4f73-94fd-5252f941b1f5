{"cells": [{"cell_type": "code", "execution_count": 6, "id": "da9fe4f5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["201\n", "{'user': {'id': 'TZ28097'}, 'token': {'expiry': 14400.0}, 'permissions': ['TUTORIAL']}\n"]}], "source": ["# WorldQuant Brain API 认证和连接设置\n", "# 导入必要的库\n", "import requests  # 用于HTTP请求\n", "import json      # 用于JSON数据处理\n", "from os.path import expanduser  # 用于获取用户主目录路径\n", "from requests.auth import HTTPBasicAuth  # 用于HTTP基本认证\n", "\n", "# 从brain.txt文件读取凭据（推荐用于生产环境）\n", "with open(expanduser('brain.txt')) as f:\n", "    credentials = json.load(f)\n", "username, password = credentials\n", "\n", "# 创建会话对象，用于保持连接状态\n", "sess = requests.Session()\n", "\n", "# 设置HTTP基本认证\n", "sess.auth = HTTPBasicAuth(username, password)\n", "\n", "# 向WorldQuant Brain API发送认证请求\n", "response = sess.post('https://api.worldquantbrain.com/authentication')\n", "\n", "# 打印响应状态码（201表示成功）\n", "print(response.status_code)\n", "# 打印响应内容，包含用户ID、令牌过期时间和权限信息\n", "print(response.json())"]}, {"cell_type": "code", "execution_count": 5, "id": "add94278", "metadata": {}, "outputs": [], "source": ["# 配置Alpha策略模拟参数\n", "simulation_data = {\n", "    'type': 'REGULAR',  # 模拟类型：常规模拟\n", "    'settings': {\n", "        'instrumentType': 'EQUITY',      # 金融工具类型：股票\n", "        'region': 'USA',                # 地区：美国市场\n", "        'universe': 'TOP3000',          # 股票池：美国前3000只股票\n", "        'delay': 1,                     # 延迟：1天（模拟真实交易延迟）\n", "        'decay': 0,                     # 衰减：0（信号不衰减）\n", "        'neutralization': 'INDUSTRY',   # 中性化：行业中性化\n", "        'truncation': 0.08,             # 截断：8%（限制极端值影响）\n", "        'pasteurization': 'ON',         # 巴氏杀菌：开启（数据清洗）\n", "        'unitHandling': 'VERIFY',       # 单位处理：验证模式\n", "        'nanHandling': 'OFF',           # NaN处理：关闭\n", "        'language': 'FASTEXPR',         # 表达式语言：FastExpr\n", "        'visualization': False          # 可视化：关闭\n", "    },\n", "    # Alpha表达式：负债与资产比率（财务健康度指标）\n", "    'regular': 'liabilities/assets'\n", "}"]}, {"cell_type": "code", "execution_count": 3, "id": "6f66f8a5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<Response [201]>\n", "{'Date': 'Wed, 03 Sep 2025 09:20:09 GMT', 'Content-Type': 'text/html; charset=UTF-8', 'Content-Length': '0', 'Connection': 'keep-alive', 'Retry-After': '2.5', 'Location': 'https://api.worldquantbrain.com/simulations/SQl9OaHM4Zea44h3htz8IN', 'Allow': 'POST, OPTIONS', 'X-Request-Id': 'f9565fabe8774747bc71e2067928a2fa', 'X-Frame-Options': 'SAMEORIGIN', 'Vary': 'Accept-Language, Cookie, Origin', 'Content-Language': 'en', 'Access-Control-Allow-Origin': 'https://platform.worldquantbrain.com', 'Access-Control-Allow-Credentials': 'true', 'Access-Control-Expose-Headers': 'Location,Retry-After', 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}\n", "{'id': 'SQl9OaHM4Zea44h3htz8IN', 'type': 'REGULAR', 'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 0, 'neutralization': 'INDUSTRY', 'truncation': 0.08, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'maxTrade': 'OFF', 'language': 'FASTEXPR', 'visualization': False}, 'regular': 'liabilities/assets', 'status': 'COMPLETE', 'alpha': 'v7WpPeQ'}\n"]}], "source": ["# 导入时间模块用于等待\n", "from time import sleep\n", "\n", "# 提交Alpha策略模拟请求\n", "sim_resp = sess.post(\n", "    'https://api.worldquantbrain.com/simulations',\n", "    json=simulation_data,  # 发送之前配置的模拟参数\n", ")\n", "\n", "# 打印响应对象和响应头信息\n", "print(sim_resp)\n", "print(sim_resp.headers)\n", "\n", "# 从响应头中获取模拟进度查询URL\n", "sim_progress_url = sim_resp.headers['Location']\n", "\n", "# 轮询模拟进度直到完成\n", "while True:\n", "    # 查询模拟进度\n", "    sim_progress_resp = sess.get(sim_progress_url)\n", "    \n", "    # 获取建议的重试等待时间\n", "    retry_after_sec = float(sim_progress_resp.headers.get(\"Retry-After\", 0))\n", "    \n", "    # 如果没有重试时间，说明模拟已完成\n", "    if retry_after_sec == 0:\n", "        break\n", "    \n", "    # 等待指定时间后再次查询\n", "    sleep(retry_after_sec)\n", "\n", "# 从响应中提取生成的Alpha ID\n", "alpha_id = sim_progress_resp.json()[\"alpha\"]\n", "\n", "# 打印完整的模拟结果\n", "print(sim_progress_resp.json())\n", "\n"]}, {"cell_type": "markdown", "id": "0ec4c75e", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 5}