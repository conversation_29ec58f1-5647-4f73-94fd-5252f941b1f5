# WorldQuant Brain Alpha策略交互式批量生成系统
# 基于day3.py核心逻辑，保持完整的Alpha生成策略过程
# 主要功能：数据字段获取、Alpha表达式生成、批量模拟、质量检测

import requests
import json
import os
import logging
from datetime import datetime
from os.path import expanduser
from time import sleep
from requests.auth import HTTPBasicAuth

# ==================== 配置常量 ====================

# 文件路径配置
QUALIFIED_ALPHA_FILE = 'qualified_alphas.txt'  # 合格Alpha ID存储文件
SIMULATION_LOG_FILE = 'simulation.log'         # 日志文件
CREDENTIALS_FILE = 'brain.txt'                 # 认证凭据文件

# ==================== 核心功能函数 ====================

def sign_in():
    """
    WorldQuant Brain API认证函数
    
    功能说明：
    - 从brain.txt文件读取用户凭据
    - 创建HTTP会话对象并配置基本认证
    - 发送认证请求并返回已认证的会话对象
    
    返回值：
    - sess: 已认证的requests.Session对象
    """
    try:
        # 从凭据文件加载用户名和密码
        with open(expanduser(CREDENTIALS_FILE)) as f:
            credentials = json.load(f)
        
        username, password = credentials
        
        # 创建HTTP会话对象
        sess = requests.Session()
        sess.auth = HTTPBasicAuth(username, password)
        
        # 发送认证请求
        response = sess.post('https://api.worldquantbrain.com/authentication')
        
        if response.status_code == 201:
            print("✅ WorldQuant Brain API认证成功")
        else:
            print(f"❌ 认证失败，状态码: {response.status_code}")
        
        return sess
        
    except FileNotFoundError:
        print("❌ 错误：找不到brain.txt凭据文件")
        print("请确保在项目根目录下创建brain.txt文件，格式为: [\"username\", \"password\"]")
        raise
    except json.JSONDecodeError:
        print("❌ 错误：brain.txt文件格式不正确")
        print("正确格式应为: [\"<EMAIL>\", \"your_password\"]")
        raise
    except Exception as e:
        print(f"❌ 认证过程发生错误: {str(e)}")
        raise

def get_datafields(s, searchScope, dataset_id='', search=''):
    """
    获取WorldQuant Brain数据字段信息
    
    参数说明：
    - s: 已认证的requests.Session对象
    - searchScope: 搜索范围配置字典
    - dataset_id: 数据集ID（如'fundamental6'）
    - search: 搜索关键词
    
    返回值：
    - pandas.DataFrame: 包含数据字段详细信息的表格
    """
    import pandas as pd
    
    instrument_type = searchScope['instrumentType']
    region = searchScope['region']
    delay = searchScope['delay']
    universe = searchScope['universe']
    
    print(f"🔍 开始获取数据字段...")
    print(f"   数据集ID: {dataset_id if dataset_id else '全部'}")
    print(f"   市场范围: {region} {universe}")
    
    # 构建API URL
    if len(search) == 0:
        url_template = "https://api.worldquantbrain.com/data-fields?" + \
                       f"&instrumentType={instrument_type}" + \
                       f"&region={region}&delay={str(delay)}&universe={universe}&dataset.id={dataset_id}&limit=50" + \
                       "&offset={x}"
        
        # 获取总记录数
        initial_response = s.get(url_template.format(x=0))
        if initial_response.status_code == 200:
            count = initial_response.json()['count']
            print(f"   总记录数: {count}")
        else:
            print(f"❌ 获取记录数失败，状态码: {initial_response.status_code}")
            count = 0
    else:
        url_template = "https://api.worldquantbrain.com/data-fields?" + \
                       f"&instrumentType={instrument_type}" + \
                       f"&region={region}&delay={str(delay)}&universe={universe}&limit=50" + \
                       f"&search={search}" + \
                       "&offset={x}"
        count = 100
    
    # 分页获取数据字段
    datafields_list = []
    pages_processed = 0
    
    for x in range(0, count, 50):
        try:
            datafields = s.get(url_template.format(x=x))
            if datafields.status_code == 200:
                results = datafields.json()['results']
                datafields_list.append(results)
                pages_processed += 1
                print(f"   已处理第 {pages_processed} 页，获取 {len(results)} 条记录")
            else:
                print(f"❌ 第 {pages_processed + 1} 页请求失败，状态码: {datafields.status_code}")
                break
        except Exception as e:
            print(f"❌ 处理第 {pages_processed + 1} 页时发生错误: {e}")
            break
    
    # 展平列表并转换为DataFrame
    datafields_list_flat = [item for sublist in datafields_list for item in sublist]
    datafields_df = pd.DataFrame(datafields_list_flat)
    
    print(f"✅ 数据字段获取完成，共 {len(datafields_df)} 条记录")
    return datafields_df

def check_alpha_quality(alpha_data):
    """
    检查Alpha是否符合7个核心质量标准（严格按照权威标准）
    
    参数说明：
    - alpha_data: Alpha详细数据，包含性能指标
    
    返回值：
    - bool: 是否符合质量标准
    - dict: 详细的检查结果
    """
    is_data = alpha_data.get('is', {})
    
    # 提取关键指标（严格按照API返回的字段名）
    sharpe = is_data.get('sharpe', 0)
    fitness = is_data.get('fitness', 0)
    turnover = is_data.get('turnover', 0) * 100  # 转换为百分比
    sub_universe_sharpe = is_data.get('subUniverseSharpe', 0)  # 注意字段名
    weight_concentration = is_data.get('maxWeight', 0) * 100  # 最大权重作为权重集中度
    ic_mean = is_data.get('ic_mean', 0)
    returns = is_data.get('returns', 0)
    
    # 7个核心质量标准检查（严格按照权威案例）
    checks = {
        # PASS标准 (4个)
        'turnover_above_min': {
            'passed': turnover >= 1.0,
            'message': f"Turnover of {turnover:.2f}% is {'above' if turnover >= 1.0 else 'below'} cutoff of 1%.",
            'status': 'PASS' if turnover >= 1.0 else 'FAIL'
        },
        'turnover_below_max': {
            'passed': turnover <= 70.0,
            'message': f"Turnover of {turnover:.2f}% is {'below' if turnover <= 70.0 else 'above'} cutoff of 70%.",
            'status': 'PASS' if turnover <= 70.0 else 'FAIL'
        },
        'sub_universe_sharpe_ok': {
            'passed': sub_universe_sharpe >= -1.54,
            'message': f"Sub-universe Sharpe of {sub_universe_sharpe:.2f} is {'above' if sub_universe_sharpe >= -1.54 else 'below'} cutoff of -1.54.",
            'status': 'PASS' if sub_universe_sharpe >= -1.54 else 'FAIL'
        },
        'competition_challenge': {
            'passed': True,  # 默认通过竞赛挑战
            'message': "Competition Challenge matches.",
            'status': 'PASS'
        },
        
        # FAIL标准 (3个) - 需要全部通过才算合格
        'sharpe_ok': {
            'passed': sharpe >= 1.25,
            'message': f"Sharpe of {sharpe:.2f} is {'above' if sharpe >= 1.25 else 'below'} cutoff of 1.25.",
            'status': 'PASS' if sharpe >= 1.25 else 'FAIL'
        },
        'fitness_ok': {
            'passed': fitness >= 1.0,
            'message': f"Fitness of {fitness:.2f} is {'above' if fitness >= 1.0 else 'below'} cutoff of 1.",
            'status': 'PASS' if fitness >= 1.0 else 'FAIL'
        },
        'weight_concentration_ok': {
            'passed': weight_concentration <= 10.0,
            'message': f"Weight concentration {weight_concentration:.0f}% is {'above' if weight_concentration <= 10.0 else 'below'} cutoff of 10% on {datetime.now().strftime('%m/%d/%Y')}.",
            'status': 'PASS' if weight_concentration <= 10.0 else 'FAIL'
        }
    }
    
    # 计算通过的检查项数量
    passed_checks = sum(1 for check in checks.values() if check['passed'])
    total_checks = len(checks)
    
    # 需要全部7个检查项都通过才算合格（严格标准）
    is_qualified = passed_checks == 7
    
    result = {
        'qualified': is_qualified,
        'passed_checks': passed_checks,
        'total_checks': total_checks,
        'metrics': {
            'sharpe': sharpe,
            'fitness': fitness,
            'turnover': turnover,
            'sub_universe_sharpe': sub_universe_sharpe,
            'weight_concentration': weight_concentration,
            'ic_mean': ic_mean,
            'returns': returns
        },
        'checks': checks
    }
    
    return is_qualified, result

def save_qualified_alpha(alpha_id, alpha_expression, quality_result, alpha_index=None):
    """
    保存合格的Alpha ID到文件（包含完整的7个标准检测结果和序号）
    
    参数说明：
    - alpha_id: Alpha ID
    - alpha_expression: Alpha表达式
    - quality_result: 质量检查结果
    - alpha_index: Alpha序号（第几个Alpha）
    """
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 准备保存的数据（包含完整的检测结果和序号）
    alpha_record = {
        'alpha_id': alpha_id,
        'expression': alpha_expression,
        'alpha_index': alpha_index,  # 添加序号
        'timestamp': timestamp,
        'qualified': quality_result['qualified'],
        'passed_checks': quality_result['passed_checks'],
        'total_checks': quality_result['total_checks'],
        'metrics': quality_result['metrics'],
        'detailed_checks': quality_result['checks']
    }
    
    # 追加到合格Alpha文件
    with open(QUALIFIED_ALPHA_FILE, 'a', encoding='utf-8') as f:
        f.write(json.dumps(alpha_record, ensure_ascii=False) + '\n')
    
    # 同时保存到专门的成功Alpha ID文件（包含序号）
    success_alpha_file = 'successful_alpha_ids.txt'
    
    # 如果文件不存在，先写入表头
    if not os.path.exists(success_alpha_file):
        with open(success_alpha_file, 'w', encoding='utf-8') as f:
            f.write("Alpha_ID\t序号\tAlpha表达式\t时间戳\t通过标准\n")
    
    with open(success_alpha_file, 'a', encoding='utf-8') as f:
        index_str = f"第{alpha_index}个" if alpha_index else "未知序号"
        f.write(f"{alpha_id}\t{index_str}\t{alpha_expression}\t{timestamp}\t{quality_result['passed_checks']}/{quality_result['total_checks']}\n")
    
    print(f"💾 已保存合格Alpha: {alpha_id} ({index_str})")
    print(f"   详细记录: {QUALIFIED_ALPHA_FILE}")
    print(f"   ID列表: {success_alpha_file}")
    
    # 特别提醒：这是完全通过7个标准的Alpha
    if quality_result['passed_checks'] == 7:
        print(f"🏆 重要发现！Alpha {alpha_id} 完全通过所有7个标准！")
        print(f"   这是第 {alpha_index} 个测试的Alpha，请重点关注！")

def display_alpha_result(alpha_id, alpha_expression, quality_result):
    """
    显示Alpha测试结果（按照WorldQuant Brain 7个标准格式）
    
    参数说明：
    - alpha_id: Alpha ID
    - alpha_expression: Alpha表达式
    - quality_result: 质量检查结果
    """
    print(f"\n" + "="*60)
    print(f"📊 Alpha测试结果 - ID: {alpha_id}")
    print(f"="*60)
    print(f"表达式: {alpha_expression}")
    print(f"质量评级: {'✅ 合格' if quality_result['qualified'] else '❌ 不合格'}")
    print(f"通过检查: {quality_result['passed_checks']}/{quality_result['total_checks']}")
    
    print(f"\n📋 IS Testing Status (IS 测试状态)")
    
    # 统计PASS和FAIL数量
    pass_count = sum(1 for check in quality_result['checks'].values() if check['status'] == 'PASS')
    fail_count = sum(1 for check in quality_result['checks'].values() if check['status'] == 'FAIL')
    
    print(f"\n✅ {pass_count} PASS ({pass_count} 及格)")
    for check_name, check_info in quality_result['checks'].items():
        if check_info['status'] == 'PASS':
            print(f"   {check_info['message']}")
    
    if fail_count > 0:
        print(f"\n❌ {fail_count} FAIL ({fail_count} 不及格)")
        for check_name, check_info in quality_result['checks'].items():
            if check_info['status'] == 'FAIL':
                print(f"   {check_info['message']}")
    
    print(f"\n📈 详细性能指标:")
    metrics = quality_result['metrics']
    print(f"   Sharpe: {metrics['sharpe']:.2f}")
    print(f"   Fitness: {metrics['fitness']:.2f}")
    print(f"   Turnover: {metrics['turnover']:.2f}%")
    print(f"   Sub-universe Sharpe: {metrics['sub_universe_sharpe']:.2f}")
    print(f"   Weight Concentration: {metrics['weight_concentration']:.0f}%")
    print(f"   IC Mean: {metrics['ic_mean']:.4f}")
    print(f"   Returns: {metrics['returns']:.4f}")
    print(f"\n" + "="*60)

def show_main_menu():
    """
    显示主菜单（3个必要模式）
    """
    print("\n" + "="*60)
    print("🚀 WorldQuant Brain Alpha策略交互式生成系统")
    print("="*60)
    print("\n📋 请选择运行模式:")
    print("1. 自动模式 - 生成、测试并提交Alpha策略")
    print("2. 提交模式 - 提交已保存的合格Alpha策略")
    print("3. 退出系统")
    
def show_dataset_menu():
    """
    显示数据集选择菜单
    """
    print("\n📊 可用数据集列表:")
    print("1. fundamental6 - 基础财务数据 (TOP3000股票)")
    print("2. analyst4 - 分析师预测数据 (TOP1000股票)")
    print("3. pv1 - 成交量数据 (TOP1000股票)")
    
def get_dataset_config(choice):
    """
    根据选择获取数据集配置
    
    参数说明：
    - choice: 用户选择的数据集编号
    
    返回值：
    - tuple: (dataset_id, searchScope)
    """
    configs = {
        '1': ('fundamental6', {
            'instrumentType': 'EQUITY',
            'region': 'USA',
            'delay': '1',
            'universe': 'TOP3000'
        }),
        '2': ('analyst4', {
            'instrumentType': 'EQUITY',
            'region': 'USA',
            'delay': '1',
            'universe': 'TOP1000'
        }),
        '3': ('pv1', {
            'instrumentType': 'EQUITY',
            'region': 'USA',
            'delay': '1',
            'universe': 'TOP1000'
        })
    }
    
    return configs.get(choice, configs['1'])

def generate_alpha_expressions(datafields_list):
    """
    生成Alpha表达式（完全按照day3.py的策略过程）
    
    参数说明：
    - datafields_list: 数据字段列表
    
    返回值：
    - list: Alpha表达式列表
    """
    print("\n🔧 配置Alpha策略构建参数...")
    
    # Alpha策略表达式模板说明（与day3.py完全一致）：
    # 基础模板: <group_compare_op>(<ts_compare_op>(<company_fundamentals>, <days>), <group>)
    # 示例: group_rank(ts_rank(fundamental_data, 252), industry)
    # 含义: 对基本面数据进行252天时间序列排名，然后在行业内进行分组排名
    
    # 分组操作符：用于在股票池内进行相对排名和标准化
    group_compare_op = [
        'group_rank',        # 分组排名：在指定分组内对数据进行排名（0-1之间）
        'group_zscore',      # 分组标准化：在指定分组内进行Z-score标准化
        'group_neutralize'   # 分组中性化：消除分组内的系统性偏差
    ]
    
    # 时间序列操作符：用于处理时间维度的数据变化
    ts_compare_op = [
        'ts_rank',      # 时间序列排名：在指定时间窗口内进行排名
        'ts_zscore',    # 时间序列标准化：在指定时间窗口内进行Z-score标准化
        'ts_av_diff'    # 时间序列平均差：计算与历史平均值的差异
    ]
    
    # 时间周期参数：定义回望窗口的长度（交易日）
    days = [
        60,   # 短期：约3个月的交易数据
        200   # 长期：约10个月的交易数据
    ]
    
    # 分组方法：定义股票分组的维度
    group = [
        'market',                        # 市场分组：整个市场作为一组
        'industry',                      # 行业分组：按GICS行业分类
        'subindustry',                   # 子行业分组：按GICS子行业分类
        'sector',                        # 板块分组：按GICS板块分类
        'densify(pv13_h_f1_sector)'     # 自定义板块分组：使用特定的板块分类方法
    ]
    
    print(f"   分组操作符: {len(group_compare_op)} 种 - {group_compare_op}")
    print(f"   时间序列操作符: {len(ts_compare_op)} 种 - {ts_compare_op}")
    print(f"   基本面字段: {len(datafields_list)} 个")
    print(f"   时间周期: {days} 天")
    print(f"   分组方法: {len(group)} 种 - {group}")
    
    print("\n🏭 开始批量生成Alpha策略表达式...")
    print("   这将创建所有可能的操作符、字段、周期和分组的组合")
    
    # 初始化Alpha表达式存储列表
    alpha_expressions = []
    expression_count = 0
    
    # 使用嵌套循环生成所有可能的Alpha表达式组合（与day3.py完全一致）
    # 计算预期总数
    total_combinations = len(group_compare_op) * len(ts_compare_op) * len(datafields_list) * len(days) * len(group)
    print(f"   预期生成表达式总数: {total_combinations:,} 个")
    
    # 五重嵌套循环：遍历所有参数组合
    for gco in group_compare_op:                    # 遍历分组比较操作符
        for tco in ts_compare_op:                   # 遍历时间序列比较操作符
            for cf in datafields_list:             # 遍历公司基本面数据字段
                for d in days:                      # 遍历时间周期
                    for grp in group:               # 遍历分组依据
                        # 构建Alpha表达式：group_op(ts_op(fundamental_field, period), group_method)
                        alpha_expression = f"{gco}({tco}({cf}, {d}), {grp})"
                        alpha_expressions.append(alpha_expression)
                        expression_count += 1
                        
                        # 每生成1000个表达式显示一次进度
                        if expression_count % 1000 == 0:
                            print(f"   已生成 {expression_count:,} 个表达式...")
    
    print(f"\n✅ Alpha表达式生成完成！")
    print(f"   实际生成表达式总数: {len(alpha_expressions):,} 个")
    print(f"   表达式示例:")
    for i, expr in enumerate(alpha_expressions[:5], 1):
        print(f"     {i}. {expr}")
    
    # 验证生成数量
    print(f"\n📊 生成统计:")
    print(f"   分组操作符数量: {len(group_compare_op)}")
    print(f"   时间序列操作符数量: {len(ts_compare_op)}")
    print(f"   基本面字段数量: {len(datafields_list)}")
    print(f"   时间周期数量: {len(days)}")
    print(f"   分组方法数量: {len(group)}")
    print(f"   理论组合数: {len(group_compare_op)} × {len(ts_compare_op)} × {len(datafields_list)} × {len(days)} × {len(group)} = {total_combinations:,}")
    
    return alpha_expressions

def create_alpha_list(alpha_expressions, searchScope):
    """
    将Alpha表达式封装为WorldQuant Brain模拟请求格式（完全按照day3.py）
    
    参数说明：
    - alpha_expressions: Alpha表达式列表
    - searchScope: 搜索范围配置
    
    返回值：
    - list: 封装好的Alpha模拟请求列表
    """
    print("\n🎯 开始Alpha策略封装和模拟准备阶段")
    print("="*60)
    print("\n📦 将Alpha表达式封装为WorldQuant Brain模拟请求格式...")
    
    # 初始化Alpha模拟请求列表
    # 每个元素包含完整的模拟配置和Alpha表达式
    alpha_list = []
    
    # 遍历所有生成的Alpha表达式，将其封装为标准的模拟请求格式
    for index, alpha_expression in enumerate(alpha_expressions, start=1):
        if index <= 5:  # 只显示前5个的详细信息
            print(f"📝 正在处理第 {index} 个Alpha表达式...")
            print(f"   表达式: {alpha_expression}")
        
        # 构建WorldQuant Brain标准模拟请求数据结构（与day3.py完全一致）
        simulation_data = {
            "type": "REGULAR",  # 模拟类型：常规Alpha策略
            "settings": {
                # ==================== 基础市场配置 ====================
                "instrumentType": searchScope['instrumentType'],    # 金融工具类型：股票
                "region": searchScope['region'],              # 目标市场：美国
                "universe": searchScope['universe'],        # 股票池：市值前3000大股票
                "delay": int(searchScope['delay']),                   # 数据延迟：1天（避免前瞻偏差）
                
                # ==================== Alpha策略参数 ====================
                "decay": 0,                   # 衰减系数：0表示无衰减
                "neutralization": "SUBINDUSTRY",  # 中性化方法：子行业中性
                "truncation": 0.01,           # 截断参数：去除极端值（1%分位数）
                "pasteurization": "ON",       # 巴氏杀菌：开启（去除异常值）
                
                # ==================== 数据处理配置 ====================
                "unitHandling": "VERIFY",     # 单位处理：验证数据单位一致性
                "nanHandling": "OFF",         # NaN处理：关闭自动处理
                "language": "FASTEXPR",       # 表达式语言：FastExpr（高性能）
                "visualization": False,       # 可视化：关闭（提高处理速度）
            },
            "regular": alpha_expression      # Alpha表达式主体
        }
        
        # 将封装好的模拟请求添加到列表中
        alpha_list.append(simulation_data)
        
        # 显示当前进度
        if index % 100 == 0 or index <= 5:
            print(f"   ✅ 已封装 {len(alpha_list)} 个Alpha策略")
    
    print(f"\n📊 Alpha策略封装完成统计：")
    print(f"   总策略数量: {len(alpha_list):,} 个")
    print(f"   封装格式: WorldQuant Brain标准模拟请求")
    print(f"   目标市场: {searchScope['region']} {searchScope['universe']}")
    print(f"   中性化方法: 子行业中性")
    
    # 显示第一个封装好的Alpha策略示例
    if alpha_list:
        print(f"\n🔍 封装示例 (第1个Alpha策略):")
        print(f"   Alpha表达式: {alpha_list[0]['regular']}")
        print(f"   完整配置: {alpha_list[0]}")
    
    return alpha_list

def simulate_single_alpha(sess, alpha_expression, searchScope):
    """
    模拟单个Alpha策略
    
    参数说明：
    - sess: 已认证的会话对象
    - alpha_expression: Alpha表达式
    - searchScope: 搜索范围配置
    
    返回值：
    - dict: 模拟结果
    """
    # 构建模拟请求数据
    simulation_data = {
        "type": "REGULAR",
        "settings": {
            "instrumentType": searchScope['instrumentType'],
            "region": searchScope['region'],
            "universe": searchScope['universe'],
            "delay": int(searchScope['delay']),
            "decay": 0,
            "neutralization": "SUBINDUSTRY",
            "truncation": 0.01,
            "pasteurization": "ON",
            "unitHandling": "VERIFY",
            "nanHandling": "OFF",
            "language": "FASTEXPR",
            "visualization": False,
        },
        "regular": alpha_expression
    }
    
    try:
        print(f"🔄 正在模拟Alpha: {alpha_expression[:50]}...")
        
        # 发送模拟请求
        sim_resp = sess.post(
            'https://api.worldquantbrain.com/simulations',
            json=simulation_data,
            timeout=30
        )
        
        if sim_resp.status_code != 201:
            print(f"❌ 模拟请求失败，状态码: {sim_resp.status_code}")
            return None
        
        # 获取模拟进度URL
        sim_progress_url = sim_resp.headers.get('Location')
        if not sim_progress_url:
            print("❌ 未获取到模拟进度URL")
            return None
        
        # 等待模拟完成
        print("⏳ 等待模拟完成...")
        start_time = datetime.now()
        
        while True:
            sim_progress_resp = sess.get(sim_progress_url)
            retry_after_sec = float(sim_progress_resp.headers.get("Retry-After", 0))
            
            if retry_after_sec == 0:  # 模拟完成
                # 检查响应状态码
                if sim_progress_resp.status_code == 200:
                    response_data = sim_progress_resp.json()
                    print(f"📋 模拟完成响应: {response_data}")
                    
                    # 尝试从不同字段获取Alpha ID
                    alpha_id = None
                    if 'alpha' in response_data:
                        alpha_id = response_data['alpha']
                    elif 'alphaId' in response_data:
                        alpha_id = response_data['alphaId']
                    elif 'id' in response_data:
                        alpha_id = response_data['id']
                    else:
                        # 从URL中提取ID作为备用方案
                        alpha_id = sim_progress_url.split('/')[-1] if '/' in sim_progress_url else 'Unknown'
                        print(f"⚠️ 无法从响应中找到Alpha ID，使用URL ID: {alpha_id}")
                    
                    print(f"✅ 模拟完成，Alpha ID: {alpha_id}")
                else:
                    print(f"❌ 模拟完成但状态码异常: {sim_progress_resp.status_code}")
                    return None
                
                # 等待指标计算完成
                sleep(3)
                
                # 获取Alpha详情
                alpha_url = f"https://api.worldquantbrain.com/alphas/{alpha_id}"
                alpha_detail = sess.get(alpha_url)
                
                if alpha_detail.status_code == 200:
                    alpha_data = alpha_detail.json()
                    return {
                        'alpha_id': alpha_id,
                        'expression': alpha_expression,
                        'data': alpha_data,
                        'success': True
                    }
                else:
                    print(f"❌ 获取Alpha详情失败，状态码: {alpha_detail.status_code}")
                    return None
            
            # 显示进度
            elapsed = (datetime.now() - start_time).total_seconds()
            progress = min(95, (elapsed / 30) * 100)
            print(f"⏳ 模拟进度: {progress:.0f}% (已等待 {elapsed:.1f} 秒)")
            
            sleep(retry_after_sec)
            
            # 超时保护
            if elapsed > 120:  # 2分钟超时
                print("⏰ 模拟超时，跳过此Alpha")
                return None
                
    except Exception as e:
        print(f"❌ 模拟过程发生错误: {str(e)}")
        return None

def view_qualified_alphas():
    """
    查看已保存的合格Alpha策略
    """
    if not os.path.exists(QUALIFIED_ALPHA_FILE):
        print("📝 暂无保存的合格Alpha策略")
        return
    
    print("\n📋 已保存的合格Alpha策略:")
    print("="*60)
    
    try:
        with open(QUALIFIED_ALPHA_FILE, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if not lines:
            print("📝 暂无保存的合格Alpha策略")
            return
        
        for i, line in enumerate(lines, 1):
            try:
                alpha_record = json.loads(line.strip())
                print(f"\n{i}. Alpha ID: {alpha_record['alpha_id']}")
                print(f"   表达式: {alpha_record['expression']}")
                print(f"   保存时间: {alpha_record['timestamp']}")
                print(f"   通过检查: {alpha_record['passed_checks']}/6")
                
                metrics = alpha_record['metrics']
                print(f"   夏普比率: {metrics['sharpe']:.3f} | 适应度: {metrics['fitness']:.3f}")
                
            except json.JSONDecodeError:
                print(f"   ❌ 第{i}行数据格式错误")
                
    except Exception as e:
        print(f"❌ 读取文件时发生错误: {str(e)}")

def setup_logging():
    """
    配置日志系统（记录所有命令行显示的内容）
    """
    logging.basicConfig(
        filename=SIMULATION_LOG_FILE,
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        filemode='a'
    )
    
    # 创建一个自定义的日志记录器，用于记录详细的Alpha测试结果
    detailed_logger = logging.getLogger('alpha_details')
    detailed_handler = logging.FileHandler('alpha_test_results.log', mode='a', encoding='utf-8')
    detailed_formatter = logging.Formatter('%(asctime)s - %(message)s')
    detailed_handler.setFormatter(detailed_formatter)
    detailed_logger.addHandler(detailed_handler)
    detailed_logger.setLevel(logging.INFO)
    
    return detailed_logger

def log_alpha_result(alpha_id, alpha_expression, quality_result, detailed_logger):
    """
    记录Alpha测试结果到详细日志文件
    
    参数说明：
    - alpha_id: Alpha ID
    - alpha_expression: Alpha表达式
    - quality_result: 质量检查结果
    - detailed_logger: 详细日志记录器
    """
    log_content = f"""
{'='*60}
Alpha测试结果 - ID: {alpha_id}
{'='*60}
表达式: {alpha_expression}
质量评级: {'合格' if quality_result['qualified'] else '不合格'}
通过检查: {quality_result['passed_checks']}/{quality_result['total_checks']}

IS Testing Status (IS 测试状态)
"""
    
    # 统计PASS和FAIL数量
    pass_count = sum(1 for check in quality_result['checks'].values() if check['status'] == 'PASS')
    fail_count = sum(1 for check in quality_result['checks'].values() if check['status'] == 'FAIL')
    
    log_content += f"\n{pass_count} PASS ({pass_count} 及格)\n"
    for check_name, check_info in quality_result['checks'].items():
        if check_info['status'] == 'PASS':
            log_content += f"   {check_info['message']}\n"
    
    if fail_count > 0:
        log_content += f"\n{fail_count} FAIL ({fail_count} 不及格)\n"
        for check_name, check_info in quality_result['checks'].items():
            if check_info['status'] == 'FAIL':
                log_content += f"   {check_info['message']}\n"
    
    log_content += f"\n详细性能指标:\n"
    metrics = quality_result['metrics']
    log_content += f"   Sharpe: {metrics['sharpe']:.2f}\n"
    log_content += f"   Fitness: {metrics['fitness']:.2f}\n"
    log_content += f"   Turnover: {metrics['turnover']:.2f}%\n"
    log_content += f"   Sub-universe Sharpe: {metrics['sub_universe_sharpe']:.2f}\n"
    log_content += f"   Weight Concentration: {metrics['weight_concentration']:.0f}%\n"
    log_content += f"   IC Mean: {metrics['ic_mean']:.4f}\n"
    log_content += f"   Returns: {metrics['returns']:.4f}\n"
    log_content += f"{'='*60}\n"
    
    detailed_logger.info(log_content)

def batch_submit_alphas(sess, alpha_list):
    """
    批量提交Alpha策略（完全按照day3.py的逻辑）
    
    参数说明：
    - sess: 已认证的会话对象
    - alpha_list: Alpha策略列表
    
    返回值：
    - tuple: (成功数量, 失败数量, 合格Alpha列表)
    """
    print("\n🚀 开始Alpha策略批量提交和模拟执行")
    print("="*60)
    print("\n⚙️ 模拟执行配置：")
    print(f"   - 处理策略数量: 全部 {len(alpha_list):,} 个Alpha策略")
    print("   - 最大重试次数: 15次")
    print("   - 自动重连机制: 启用")
    print("   - 错误处理: 全面覆盖")
    print("   - 日志记录: 启用")
    
    # Alpha提交失败容忍度配置
    alpha_fail_attempt_tolerance = 15  # 每个Alpha允许的最大失败尝试次数
    
    # 初始化提交统计计数器
    total_processed = 0      # 总处理数量
    successful_submissions = 0  # 成功提交数量
    failed_submissions = 0   # 失败提交数量
    qualified_alphas = []    # 合格的Alpha列表
    
    print(f"\n🎯 开始批量提交Alpha策略 (处理全部 {len(alpha_list):,} 个策略)")
    print(f"   失败容忍度: {alpha_fail_attempt_tolerance} 次重试")
    
    # 遍历Alpha策略列表进行批量提交（只处理前几个进行测试）
    max_test_count = min(5, len(alpha_list))  # 最多测试5个Alpha
    print(f"\n🧪 测试模式：只处理前 {max_test_count} 个Alpha进行质量检测")
    
    for alpha_index, alpha in enumerate(alpha_list[:max_test_count], start=1):
        total_processed += 1
        
        print(f"\n" + "-"*50)
        print(f"📤 正在处理第 {alpha_index} 个Alpha策略")
        print(f"   Alpha表达式: {alpha['regular']}")
        
        # 记录开始处理的日志
        logging.info(f"开始处理Alpha {alpha_index}: {alpha['regular']}")
        
        # ==================== 智能重试机制 ====================
        
        keep_trying = True       # 控制while循环继续的标志
        failure_count = 0        # 记录当前Alpha的失败尝试次数
        submission_successful = False  # 提交成功标志
        alpha_id = None
        
        # 实施智能重试循环：处理网络问题、认证失效、服务器错误等情况
        while keep_trying:
            try:
                print(f"\n🔄 尝试提交 (第 {failure_count + 1} 次)...")
                
                # 向WorldQuant Brain API发送Alpha模拟请求
                # 这是核心的API调用，将Alpha策略提交给服务器进行回测
                sim_resp = sess.post(
                    'https://api.worldquantbrain.com/simulations',
                    json=alpha,  # 将当前Alpha策略（JSON格式）发送到服务器
                    timeout=30   # 设置30秒超时，避免长时间等待
                )
                
                # ==================== 成功响应处理 ====================
                
                # 检查响应状态码，201表示成功创建模拟
                if sim_resp.status_code == 201:
                    # 从响应头中获取模拟进度跟踪URL
                    # Location头包含了模拟任务的唯一标识符和跟踪链接
                    sim_progress_url = sim_resp.headers.get('Location', 'URL not found')
                    
                    # 记录成功信息到日志和控制台
                    success_message = f'✅ Alpha {alpha_index} 提交成功！Location: {sim_progress_url}'
                    logging.info(success_message)
                    print(success_message)
                    
                    # 显示详细的响应信息
                    print(f"   状态码: {sim_resp.status_code}")
                    
                    # 按照YHYYDS666项目的逻辑等待模拟完成并获取Alpha ID
                    try:
                        start_time = datetime.now()
                        total_wait = 0
                        alpha_id = None
                        
                        print(f"   ⏳ 等待模拟完成...")
                        
                        while True:
                            sim_progress_resp = sess.get(sim_progress_url)
                            retry_after_sec = float(sim_progress_resp.headers.get("Retry-After", 0))
                            
                            if retry_after_sec == 0:  # simulation done!
                                # 检查响应状态码
                                if sim_progress_resp.status_code == 200:
                                    response_data = sim_progress_resp.json()
                                    print(f"   📋 模拟完成响应: {response_data}")
                                    
                                    # 尝试从不同字段获取Alpha ID
                                    alpha_id = None
                                    if 'alpha' in response_data:
                                        alpha_id = response_data['alpha']
                                    elif 'alphaId' in response_data:
                                        alpha_id = response_data['alphaId']
                                    elif 'id' in response_data:
                                        alpha_id = response_data['id']
                                    else:
                                        # 从URL中提取ID作为备用方案
                                        alpha_id = sim_progress_url.split('/')[-1] if '/' in sim_progress_url else 'Unknown'
                                        print(f"   ⚠️ 无法从响应中找到Alpha ID，使用URL ID: {alpha_id}")
                                    
                                    print(f"   ✅ 获得 Alpha ID: {alpha_id}")
                                    break
                                else:
                                    print(f"   ❌ 模拟完成但状态码异常: {sim_progress_resp.status_code}")
                                    alpha_id = sim_progress_url.split('/')[-1] if '/' in sim_progress_url else 'Unknown'
                                    break
                            
                            # 更新等待时间和进度
                            total_wait += retry_after_sec
                            elapsed = (datetime.now() - start_time).total_seconds()
                            progress = min(95, (elapsed / 30) * 100)  # 假设通常需要 30 秒完成
                            print(f"   ⏳ 等待模拟结果... ({elapsed:.1f} 秒 | 进度约 {progress:.0f}%)")
                            sleep(retry_after_sec)
                            
                            # 防止无限等待
                            if elapsed > 120:  # 最多等待2分钟
                                print(f"   ⚠️ 模拟超时，但Alpha已提交")
                                alpha_id = sim_progress_url.split('/')[-1] if '/' in sim_progress_url else 'Unknown'
                                break
                        
                        # 更新统计计数器
                        successful_submissions += 1
                        submission_successful = True
                        keep_trying = False  # 成功提交，退出重试循环
                        
                    except Exception as e:
                        print(f"   ⚠️ 获取Alpha ID时出错: {str(e)}，但Alpha已成功提交")
                        alpha_id = sim_progress_url.split('/')[-1] if '/' in sim_progress_url else 'Unknown'
                        successful_submissions += 1
                        submission_successful = True
                        keep_trying = False
                    
                else:
                    # ==================== 失败响应处理 ====================
                    
                    error_message = f"❌ Alpha {alpha_index} 提交失败，状态码: {sim_resp.status_code}"
                    print(error_message)
                    print(f"   响应内容: {sim_resp.text[:200]}...")  # 显示前200字符
                    logging.error(f"{error_message}, 响应: {sim_resp.text}")
                    
                    # 根据不同的HTTP状态码采取相应措施
                    if sim_resp.status_code in [401, 403]:
                        # 认证问题：重新登录
                        print("🔐 检测到认证问题，尝试重新登录...")
                        sess = sign_in()
                    elif sim_resp.status_code >= 500:
                        # 服务器错误：通常是临时性问题
                        print("🔧 服务器错误，这通常是临时性问题")
                    
                    failure_count += 1
                    
            except Exception as e:
                # ==================== 通用异常处理 ====================
                
                # 处理所有其他未预期的异常
                general_error = f"🚨 Alpha {alpha_index} 发生未预期异常: {type(e).__name__}: {str(e)}"
                logging.error(general_error)
                print(general_error)
                print("   尝试重新建立会话...")
                
                failure_count += 1
            
            # ==================== 重试逻辑和失败处理 ====================
            
            # 检查是否达到失败容忍上限
            if failure_count >= alpha_fail_attempt_tolerance:
                # 达到最大重试次数，放弃当前Alpha
                final_error = f"💔 Alpha {alpha_index} 达到最大重试次数 ({alpha_fail_attempt_tolerance})，放弃处理"
                logging.error(final_error)
                print(final_error)
                
                # 尝试重新登录会话，为下一个Alpha做准备
                print("   尝试重新登录以处理下一个Alpha...")
                sess = sign_in()
                
                # 重置失败计数器，继续处理下一个Alpha
                failure_count = 0
                failed_submissions += 1
                keep_trying = False  # 退出while循环，移动到下一个Alpha
                
                break  # 退出重试循环
            
            # 如果需要重试且未达到上限，添加延迟避免过于频繁的请求
            if keep_trying and not submission_successful:
                # 实施指数退避策略：重试间隔逐渐增加
                wait_time = min(2 ** (failure_count - 1), 15)  # 最大等待15秒
                print(f"   等待 {wait_time} 秒后进行第 {failure_count + 1} 次重试...")
                sleep(wait_time)
        
        # ==================== 单个Alpha处理完成 ====================
        
        completion_message = f"📊 Alpha {alpha_index} 处理完成"
        if submission_successful:
            completion_message += " - ✅ 成功"
            print(completion_message)
            logging.info(completion_message)
            
            # 立即进行质量检测
            print(f"\n🔍 开始对Alpha {alpha_id} 进行7个标准质量检测...")
            
            try:
                # 等待一下让指标计算完成
                sleep(5)
                
                alpha_url = f"https://api.worldquantbrain.com/alphas/{alpha_id}"
                alpha_detail = sess.get(alpha_url)
                
                if alpha_detail.status_code == 200:
                    alpha_data = alpha_detail.json()
                    
                    # 检查是否有 is 字段
                    if 'is' not in alpha_data:
                        print(f"❌ Alpha {alpha_id} 无法获取指标数据，可能还在计算中")
                        print(f"   建议稍后手动检查Alpha ID: {alpha_id}")
                    else:
                        print(f"✅ 获取到Alpha {alpha_id} 的性能指标")
                        
                        # 进行7个标准的质量检测
                        is_qualified, quality_result = check_alpha_quality(alpha_data)
                        
                        # 显示测试结果
                        display_alpha_result(alpha_id, alpha['regular'], quality_result)
                        
                        # 记录到详细日志
                        detailed_logger = logging.getLogger('alpha_details')
                        log_alpha_result(alpha_id, alpha['regular'], quality_result, detailed_logger)
                        
                        # 如果合格，保存到文件
                        if is_qualified:
                            save_qualified_alpha(alpha_id, alpha['regular'], quality_result, alpha_index)
                            print(f"🎉 Alpha {alpha_id} 通过质量检测并已保存！")
                            qualified_alphas.append({
                                'alpha_id': alpha_id,
                                'expression': alpha['regular'],
                                'index': alpha_index,
                                'qualified': True
                            })
                        else:
                            print(f"❌ Alpha {alpha_id} 未通过质量检测")
                            qualified_alphas.append({
                                'alpha_id': alpha_id,
                                'expression': alpha['regular'],
                                'index': alpha_index,
                                'qualified': False
                            })
                        
                        # 记录到基础日志
                        logging.info(f"Alpha {alpha_id}: {'合格' if is_qualified else '不合格'} - {quality_result['passed_checks']}/{quality_result['total_checks']} 通过")
                        
                else:
                    print(f"❌ 无法获取Alpha {alpha_id} 的详细结果，状态码: {alpha_detail.status_code}")
                    
            except Exception as e:
                print(f"❌ 检测Alpha {alpha_id} 时发生错误: {str(e)}")
                logging.error(f"检测Alpha {alpha_id} 时发生错误: {str(e)}")
        else:
            completion_message += " - ❌ 失败"
            print(completion_message)
            logging.info(completion_message)
        
        # 显示当前处理进度
        print(f"\n📊 当前进度: {total_processed}/{max_test_count}")
        print(f"   成功: {successful_submissions}, 失败: {failed_submissions}")
        if qualified_alphas:
            qualified_count = sum(1 for alpha in qualified_alphas if alpha.get('qualified', False))
            print(f"   合格: {qualified_count}")
    
    # ==================== 批量提交总结报告 ====================
    
    print(f"\n" + "="*60)
    print(f"🏁 Alpha策略批量提交完成")
    print(f"="*60)
    
    # 生成详细的统计报告
    print(f"\n📈 提交统计报告：")
    print(f"   总处理数量: {total_processed} 个Alpha策略")
    print(f"   成功提交: {successful_submissions} 个 ({successful_submissions/total_processed*100:.1f}%)")
    print(f"   提交失败: {failed_submissions} 个 ({failed_submissions/total_processed*100:.1f}%)")
    
    if total_processed > 0:
        success_rate = successful_submissions / total_processed * 100
        print(f"   整体成功率: {success_rate:.1f}%")
    
    # 记录最终统计到日志
    final_log = f"批量提交完成 - 总数:{total_processed}, 成功:{successful_submissions}, 失败:{failed_submissions}"
    logging.info(final_log)
    
    return successful_submissions, failed_submissions, qualified_alphas

def main():
    """
    主程序入口（3个必要模式）
    """
    # 配置日志
    detailed_logger = setup_logging()
    
    try:
        # 认证
        print("🔐 正在进行API认证...")
        sess = sign_in()
        
        while True:
            show_main_menu()
            
            try:
                choice = input("\n请选择模式 (1-3): ").strip()
                
                if choice == '3':
                    print("👋 感谢使用，再见！")
                    break
                elif choice == '2':
                    # 提交模式 - 提交已保存的合格Alpha策略
                    view_qualified_alphas()
                    input("\n按回车键继续...")
                    continue
                elif choice == '1':
                    # 自动模式：生成、测试并提交Alpha策略
                    show_dataset_menu()
                    dataset_choice = input("\n请选择数据集 (1-3): ").strip()
                    
                    dataset_id, searchScope = get_dataset_config(dataset_choice)
                    
                    print(f"\n" + "="*60)
                    print(f"🎯 开始Alpha策略数据准备阶段")
                    print(f"="*60)
                    print(f"\n📊 开始处理数据集: {dataset_id}")
                    
                    # 获取数据字段（完全按照day3.py的流程）
                    datafields_df = get_datafields(sess, searchScope, dataset_id)
                    
                    if datafields_df.empty:
                        print("❌ 未获取到数据字段，请检查网络连接")
                        continue
                    
                    # 过滤MATRIX类型字段
                    matrix_fields = datafields_df[datafields_df['type'] == "MATRIX"]
                    if matrix_fields.empty:
                        print("❌ 未找到MATRIX类型的数据字段")
                        continue
                    
                    datafields_list = matrix_fields['id'].values
                    print(f"\n✅ 数据字段筛选完成：")
                    print(f"   总MATRIX字段数: {len(datafields_list)} 个")
                    print(f"   示例字段: {list(datafields_list[:5])}")
                    print(f"   完整字段列表: {datafields_list}")
                    
                    # 生成Alpha表达式（完全按照day3.py的流程）
                    alpha_expressions = generate_alpha_expressions(datafields_list)
                    
                    # 封装Alpha策略（完全按照day3.py的流程）
                    alpha_list = create_alpha_list(alpha_expressions, searchScope)
                    
                    # 自动模式：批量提交并进行质量检测
                    print("\n🚀 自动模式：开始批量提交并测试Alpha策略")
                    successful, failed, qualified_alphas = batch_submit_alphas(sess, alpha_list)
                    
                    # 显示最终统计
                    if qualified_alphas:
                        truly_qualified = sum(1 for alpha in qualified_alphas if alpha.get('qualified', False))
                        
                        print(f"\n" + "="*60)
                        print(f"🏁 Alpha策略质量检测完成")
                        print(f"="*60)
                        print(f"📈 最终统计报告：")
                        print(f"   总提交数量: {successful} 个Alpha策略")
                        print(f"   质量检测数量: {len(qualified_alphas)} 个")
                        print(f"   真正合格数量: {truly_qualified} 个")
                        if len(qualified_alphas) > 0:
                            print(f"   合格率: {truly_qualified/len(qualified_alphas)*100:.1f}%")
                        print(f"\n📁 文件输出：")
                        print(f"   合格Alpha详情: {QUALIFIED_ALPHA_FILE}")
                        print(f"   成功Alpha ID列表: successful_alpha_ids.txt")
                        print(f"   详细测试日志: alpha_test_results.log")
                        print(f"   基础日志: {SIMULATION_LOG_FILE}")
                        
                        # 记录最终统计到日志
                        final_log = f"质量检测完成 - 提交:{successful}, 检测:{len(qualified_alphas)}, 合格:{truly_qualified}"
                        logging.info(final_log)
                    else:
                        print(f"\n❌ 没有成功提交的Alpha策略")
                    
                    input("\n按回车键继续...")
                    
                else:
                    print("❌ 无效选择，请输入1-3之间的数字")
                    
            except KeyboardInterrupt:
                print("\n\n👋 用户中断，退出程序")
                break
            except Exception as e:
                print(f"❌ 程序运行出错: {str(e)}")
                logging.error(f"程序运行出错: {str(e)}")
                input("\n按回车键继续...")
                
    except Exception as e:
        print(f"❌ 程序初始化失败: {str(e)}")
        logging.error(f"程序初始化失败: {str(e)}")

if __name__ == "__main__":
    main()