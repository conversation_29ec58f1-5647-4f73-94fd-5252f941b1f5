# WorldQuant Brain Alpha策略批量生成和回测系统
# 本脚本实现了基于fundamental6数据集的批量Alpha策略生成、提交和监控功能
# 主要功能：API认证、数据字段获取、策略模板生成、批量回测提交、错误处理和重试机制

import requests
import json
from os.path import expanduser
from requests.auth import HTTPBasicAuth


def sign_in():
    """
    WorldQuant Brain API认证函数
    
    功能说明：
    - 从brain.txt文件读取用户凭据（用户名和密码）
    - 创建HTTP会话对象并配置基本认证
    - 发送认证请求并返回已认证的会话对象
    
    返回值：
    - sess: 已认证的requests.Session对象，用于后续API调用
    
    异常处理：
    - 如果凭据文件不存在或格式错误，将抛出相应异常
    - 如果认证失败，将在响应中显示错误状态码
    """
    try:
        # 从brain.txt文件加载用户凭据（推荐用于生产环境的安全做法）
        with open(expanduser('brain.txt')) as f:
            credentials = json.load(f)
        
        # 从凭据列表中提取用户名和密码
        username, password = credentials
        
        # 创建HTTP会话对象，用于维持连接状态和认证信息
        sess = requests.Session()
        
        # 配置HTTP基本认证（用户名和密码）
        sess.auth = HTTPBasicAuth(username, password)
        
        # 向WorldQuant Brain API发送认证请求
        response = sess.post('https://api.worldquantbrain.com/authentication')
        
        # 打印认证结果（状态码201表示成功认证）
        print(f"认证状态码: {response.status_code}")
        print(f"认证响应: {response.json()}")
        
        # 检查认证是否成功
        if response.status_code == 201:
            print("✅ WorldQuant Brain API认证成功")
        else:
            print(f"❌ 认证失败，状态码: {response.status_code}")
        
        return sess
        
    except FileNotFoundError:
        print("❌ 错误：找不到brain.txt凭据文件")
        print("请确保在项目根目录下创建brain.txt文件，格式为: [\"username\", \"password\"]")
        raise
    except json.JSONDecodeError:
        print("❌ 错误：brain.txt文件格式不正确")
        print("正确格式应为: [\"<EMAIL>\", \"your_password\"]")
        raise
    except ValueError as e:
        print(f"❌ 错误：凭据格式不正确 - {e}")
        print("请确保brain.txt包含用户名和密码两个元素")
        raise


sess = sign_in()


def get_datafields(s, searchScope, dataset_id: str = '', search: str = ''):
    """
    获取WorldQuant Brain数据字段信息的核心函数
    
    功能说明：
    - 从指定数据集（如fundamental6）获取所有可用的数据字段
    - 支持按关键词搜索特定数据字段
    - 使用分页机制处理大量数据字段（每页50条记录）
    - 返回结构化的DataFrame便于后续分析和处理
    
    参数说明：
    - s: 已认证的requests.Session对象
    - searchScope: 搜索范围配置字典，包含以下键值：
        * instrumentType: 金融工具类型（如'EQUITY'表示股票）
        * region: 地区代码（如'USA'表示美国市场）
        * delay: 数据延迟天数（如1表示1天延迟）
        * universe: 股票池范围（如'TOP3000'表示前3000大股票）
    - dataset_id: 数据集ID（如'fundamental6'表示公司基本面数据）
    - search: 可选的搜索关键词，用于筛选特定数据字段
    
    返回值：
    - pandas.DataFrame: 包含数据字段详细信息的表格，包括字段ID、描述、类型等
    
    数据字段类型说明：
    - MATRIX: 矩阵类型数据，适用于Alpha策略构建
    - VECTOR: 向量类型数据
    - SCALAR: 标量类型数据
    """
    import pandas as pd
    
    # 从搜索范围配置中提取API请求参数
    instrument_type = searchScope['instrumentType']  # 金融工具类型
    region = searchScope['region']                  # 地区
    delay = searchScope['delay']                    # 数据延迟
    universe = searchScope['universe']              # 股票池
    
    print(f"🔍 开始获取数据字段...")
    print(f"   数据集ID: {dataset_id if dataset_id else '全部'}")
    print(f"   搜索关键词: {search if search else '无'}")
    print(f"   市场范围: {region} {universe}")
    
    # 根据是否提供搜索关键词构建不同的API URL
    if len(search) == 0:
        # 无搜索关键词：获取指定数据集的所有字段
        url_template = "https://api.worldquantbrain.com/data-fields?" + \
                       f"&instrumentType={instrument_type}" + \
                       f"&region={region}&delay={str(delay)}&universe={universe}&dataset.id={dataset_id}&limit=50" + \
                       "&offset={x}"
        # 首先获取总记录数以确定分页次数
        initial_response = s.get(url_template.format(x=0))
        if initial_response.status_code == 200:
            count = initial_response.json()['count']
            print(f"   总记录数: {count}")
        else:
            print(f"❌ 获取记录数失败，状态码: {initial_response.status_code}")
            count = 0
    else:
        # 有搜索关键词：按关键词搜索字段（使用固定限制）
        url_template = "https://api.worldquantbrain.com/data-fields?" + \
                       f"&instrumentType={instrument_type}" + \
                       f"&region={region}&delay={str(delay)}&universe={universe}&limit=50" + \
                       f"&search={search}" + \
                       "&offset={x}"
        count = 100  # 搜索结果使用固定限制
        print(f"   搜索模式，最大记录数: {count}")
    
    # 分页获取所有数据字段信息
    datafields_list = []
    pages_processed = 0
    
    for x in range(0, count, 50):  # 每页50条记录
        try:
            datafields = s.get(url_template.format(x=x))
            if datafields.status_code == 200:
                results = datafields.json()['results']
                datafields_list.append(results)
                pages_processed += 1
                print(f"   已处理第 {pages_processed} 页，获取 {len(results)} 条记录")
            else:
                print(f"❌ 第 {pages_processed + 1} 页请求失败，状态码: {datafields.status_code}")
                break
        except Exception as e:
            print(f"❌ 处理第 {pages_processed + 1} 页时发生错误: {e}")
            break
    
    # 将嵌套列表展平为一维列表
    datafields_list_flat = [item for sublist in datafields_list for item in sublist]
    
    # 转换为pandas DataFrame便于数据分析
    datafields_df = pd.DataFrame(datafields_list_flat)
    
    print(f"✅ 数据字段获取完成，共 {len(datafields_df)} 条记录")
    
    return datafields_df


# ==================== Alpha策略数据准备阶段 ====================

# 配置数据字段搜索范围
# 定义从WorldQuant Brain获取数据字段时的筛选条件
searchScope = {
    'instrumentType': 'EQUITY',  # 金融工具类型：股票（EQUITY）
    'region': 'USA',            # 目标市场：美国股票市场
    'delay': '1',               # 数据延迟：1天（避免前瞻偏差）
    'universe': 'TOP3000'       # 股票池范围：市值前3000大的股票
}

print("\n" + "="*60)
print("🎯 开始Alpha策略数据准备阶段")
print("="*60)

# 获取fundamental6数据集中的所有MATRIX类型数据字段
# fundamental6包含公司基本面数据，如财务报表、估值指标等
# MATRIX类型字段适合用于构建横截面Alpha策略
print("\n📊 获取基本面数据字段...")
fnd6 = get_datafields(s=sess, searchScope=searchScope, dataset_id='fundamental6')

# 过滤出类型为"MATRIX"的数据字段
# MATRIX类型表示该字段包含所有股票在某个时间点的横截面数据
fnd6 = fnd6[fnd6['type'] == "MATRIX"]

# 提取数据字段的ID并转换为列表，用于后续Alpha表达式生成
datafields_list_fnd6 = fnd6['id'].values

print(f"\n✅ 数据字段筛选完成：")
print(f"   总MATRIX字段数: {len(datafields_list_fnd6)} 个")
print(f"   示例字段: {list(datafields_list_fnd6[:5])}")
print(f"   完整字段列表: {datafields_list_fnd6}")


# ==================== Alpha策略表达式构建配置 ====================

print("\n🔧 配置Alpha策略构建参数...")

# Alpha策略表达式模板说明：
# 基础模板: <group_compare_op>(<ts_compare_op>(<company_fundamentals>, <days>), <group>)
# 示例: group_rank(ts_rank(fundamental_data, 252), industry)
# 含义: 对基本面数据进行252天时间序列排名，然后在行业内进行分组排名

# 分组操作符：用于在股票池内进行相对排名和标准化
group_compare_op = [
    'group_rank',        # 分组排名：在指定分组内对数据进行排名（0-1之间）
    'group_zscore',      # 分组标准化：在指定分组内进行Z-score标准化
    'group_neutralize'   # 分组中性化：消除分组内的系统性偏差
]

# 时间序列操作符：用于处理时间维度的数据变化
ts_compare_op = [
    'ts_rank',      # 时间序列排名：在指定时间窗口内进行排名
    'ts_zscore',    # 时间序列标准化：在指定时间窗口内进行Z-score标准化
    'ts_av_diff'    # 时间序列平均差：计算与历史平均值的差异
]

# 公司基本面数据字段列表（从fundamental6数据集获取的MATRIX类型字段）
company_fundamentals = datafields_list_fnd6

# 时间周期参数：定义回望窗口的长度（交易日）
days = [
    60,   # 短期：约3个月的交易数据
    200   # 长期：约10个月的交易数据
]

# 分组方法：定义股票分组的维度
group = [
    'market',                        # 市场分组：整个市场作为一组
    'industry',                      # 行业分组：按GICS行业分类
    'subindustry',                   # 子行业分组：按GICS子行业分类
    'sector',                        # 板块分组：按GICS板块分类
    'densify(pv13_h_f1_sector)'     # 自定义板块分组：使用特定的板块分类方法
]

print(f"   分组操作符: {len(group_compare_op)} 种 - {group_compare_op}")
print(f"   时间序列操作符: {len(ts_compare_op)} 种 - {ts_compare_op}")
print(f"   基本面字段: {len(company_fundamentals)} 个")
print(f"   时间周期: {days} 天")
print(f"   分组方法: {len(group)} 种 - {group}")
# ==================== Alpha策略表达式批量生成 ====================

print("\n🏭 开始批量生成Alpha策略表达式...")
print("   这将创建所有可能的操作符、字段、周期和分组的组合")

# 初始化Alpha表达式存储列表
alpha_expressions = []
expression_count = 0

# 使用嵌套循环生成所有可能的Alpha表达式组合
# 计算预期总数
total_combinations = len(group_compare_op) * len(ts_compare_op) * len(company_fundamentals) * len(days) * len(group)
print(f"   预期生成表达式总数: {total_combinations:,} 个")

# 五重嵌套循环：遍历所有参数组合
for gco in group_compare_op:                    # 遍历分组比较操作符
    for tco in ts_compare_op:                   # 遍历时间序列比较操作符
        for cf in company_fundamentals:        # 遍历公司基本面数据字段
            for d in days:                      # 遍历时间周期
                for grp in group:               # 遍历分组依据
                    # 构建Alpha表达式：group_op(ts_op(fundamental_field, period), group_method)
                    alpha_expression = f"{gco}({tco}({cf}, {d}), {grp})"
                    alpha_expressions.append(alpha_expression)
                    expression_count += 1
                    
                    # 每生成1000个表达式显示一次进度
                    if expression_count % 1000 == 0:
                        print(f"   已生成 {expression_count:,} 个表达式...")

print(f"\n✅ Alpha表达式生成完成！")
print(f"   实际生成表达式总数: {len(alpha_expressions):,} 个")
print(f"   表达式示例:")
for i, expr in enumerate(alpha_expressions[:5], 1):
    print(f"     {i}. {expr}")

# 验证生成数量
print(f"\n📊 生成统计:")
print(f"   分组操作符数量: {len(group_compare_op)}")
print(f"   时间序列操作符数量: {len(ts_compare_op)}")
print(f"   基本面字段数量: {len(company_fundamentals)}")
print(f"   时间周期数量: {len(days)}")
print(f"   分组方法数量: {len(group)}")
print(f"   理论组合数: {len(group_compare_op)} × {len(ts_compare_op)} × {len(company_fundamentals)} × {len(days)} × {len(group)} = {total_combinations:,}")

# ==================== Alpha策略封装和模拟准备阶段 ====================

print("\n🎯 开始Alpha策略封装和模拟准备阶段")
print("="*60)
print("\n📦 将Alpha表达式封装为WorldQuant Brain模拟请求格式...")

# 初始化Alpha模拟请求列表
# 每个元素包含完整的模拟配置和Alpha表达式
alpha_list = []

# 遍历所有生成的Alpha表达式，将其封装为标准的模拟请求格式
for index, alpha_expression in enumerate(alpha_expressions, start=1):
    print(f"📝 正在处理第 {index} 个Alpha表达式...")
    print(f"   表达式: {alpha_expression}")
    
    # 构建WorldQuant Brain标准模拟请求数据结构
    simulation_data = {
        "type": "REGULAR",  # 模拟类型：常规Alpha策略
        "settings": {
            # ==================== 基础市场配置 ====================
            "instrumentType": "EQUITY",    # 金融工具类型：股票
            "region": "USA",              # 目标市场：美国
            "universe": "TOP3000",        # 股票池：市值前3000大股票
            "delay": 1,                   # 数据延迟：1天（避免前瞻偏差）
            
            # ==================== Alpha策略参数 ====================
            "decay": 0,                   # 衰减系数：0表示无衰减
            "neutralization": "SUBINDUSTRY",  # 中性化方法：子行业中性
            "truncation": 0.01,           # 截断参数：去除极端值（1%分位数）
            "pasteurization": "ON",       # 巴氏杀菌：开启（去除异常值）
            
            # ==================== 数据处理配置 ====================
            "unitHandling": "VERIFY",     # 单位处理：验证数据单位一致性
            "nanHandling": "OFF",         # NaN处理：关闭自动处理
            "language": "FASTEXPR",       # 表达式语言：FastExpr（高性能）
            "visualization": False,       # 可视化：关闭（提高处理速度）
        },
        "regular": alpha_expression      # Alpha表达式主体
    }
    
    # 将封装好的模拟请求添加到列表中
    alpha_list.append(simulation_data)
    
    # 显示当前进度
    if index % 100 == 0 or index <= 5:
        print(f"   ✅ 已封装 {len(alpha_list)} 个Alpha策略")

print(f"\n📊 Alpha策略封装完成统计：")
print(f"   总策略数量: {len(alpha_list):,} 个")
print(f"   封装格式: WorldQuant Brain标准模拟请求")
print(f"   目标市场: 美国股票市场 (TOP3000)")
print(f"   中性化方法: 子行业中性")

# 显示第一个封装好的Alpha策略示例
print(f"\n🔍 封装示例 (第1个Alpha策略):")
print(f"   Alpha表达式: {alpha_list[0]['regular']}")
print(f"   完整配置: {alpha_list[0]}")

# ==================== Alpha策略批量提交和模拟执行 ====================

print("\n🚀 开始Alpha策略批量提交和模拟执行")
print("="*60)
print("\n⚙️ 模拟执行配置：")
print(f"   - 处理策略数量: 全部 {len(alpha_list):,} 个Alpha策略")
print("   - 最大重试次数: 15次")
print("   - 自动重连机制: 启用")
print("   - 错误处理: 全面覆盖")
print("   - 日志记录: 启用")

# ==================== 日志系统配置 ====================

# 导入必要的模块
import logging
from time import sleep

# 配置日志记录系统：记录Alpha提交过程中的所有关键事件
logging.basicConfig(
    filename='simulation.log',                    # 日志文件名
    level=logging.INFO,                          # 日志级别：INFO及以上
    format='%(asctime)s - %(levelname)s - %(message)s',  # 日志格式：时间-级别-消息
    filemode='a'                                 # 追加模式：保留历史日志
)

print("\n📝 日志系统已配置：")
print("   - 日志文件: simulation.log")
print("   - 日志级别: INFO")
print("   - 记录内容: 提交状态、错误信息、重试过程")

# ==================== Alpha策略提交核心逻辑 ====================

# Alpha提交失败容忍度配置
alpha_fail_attempt_tolerance = 15  # 每个Alpha允许的最大失败尝试次数

# 初始化提交统计计数器
total_processed = 0      # 总处理数量
successful_submissions = 0  # 成功提交数量
failed_submissions = 0   # 失败提交数量

print(f"\n🎯 开始批量提交Alpha策略 (处理全部 {len(alpha_list):,} 个策略)")
print(f"   失败容忍度: {alpha_fail_attempt_tolerance} 次重试")

# 遍历Alpha策略列表进行批量提交（处理所有策略）
for alpha_index, alpha in enumerate(alpha_list, start=1):
    total_processed += 1
    
    print(f"\n" + "-"*50)
    print(f"📤 正在处理第 {alpha_index} 个Alpha策略")
    print(f"   Alpha表达式: {alpha['regular']}")
    
    # 记录开始处理的日志
    logging.info(f"开始处理Alpha {alpha_index}: {alpha['regular']}")
    
    # ==================== 智能重试机制 ====================
    
    keep_trying = True       # 控制while循环继续的标志
    failure_count = 0        # 记录当前Alpha的失败尝试次数
    submission_successful = False  # 提交成功标志

    # 实施智能重试循环：处理网络问题、认证失效、服务器错误等情况
    while keep_trying:
        try:
            print(f"\n🔄 尝试提交 (第 {failure_count + 1} 次)...")
            
            # 向WorldQuant Brain API发送Alpha模拟请求
            # 这是核心的API调用，将Alpha策略提交给服务器进行回测
            sim_resp = sess.post(
                'https://api.worldquantbrain.com/simulations',
                json=alpha,  # 将当前Alpha策略（JSON格式）发送到服务器
                timeout=30   # 设置30秒超时，避免长时间等待
            )
            
            # ==================== 成功响应处理 ====================
            
            # 检查响应状态码，201表示成功创建模拟
            if sim_resp.status_code == 201:
                # 从响应头中获取模拟进度跟踪URL
                # Location头包含了模拟任务的唯一标识符和跟踪链接
                sim_progress_url = sim_resp.headers.get('Location', 'URL not found')
                
                # 记录成功信息到日志和控制台
                success_message = f'✅ Alpha {alpha_index} 提交成功！Location: {sim_progress_url}'
                logging.info(success_message)
                print(success_message)
                
                # 显示详细的响应信息
                print(f"   状态码: {sim_resp.status_code}")
                print(f"   模拟ID: {sim_progress_url.split('/')[-1] if '/' in sim_progress_url else 'Unknown'}")
                
                # 更新统计计数器
                successful_submissions += 1
                submission_successful = True
                keep_trying = False  # 成功提交，退出重试循环
                
            else:
                # ==================== 失败响应处理 ====================
                
                error_message = f"❌ Alpha {alpha_index} 提交失败，状态码: {sim_resp.status_code}"
                print(error_message)
                print(f"   响应内容: {sim_resp.text[:200]}...")  # 显示前200字符
                logging.error(f"{error_message}, 响应: {sim_resp.text}")
                
                # 根据不同的HTTP状态码采取相应措施
                if sim_resp.status_code in [401, 403]:
                    # 认证问题：重新登录
                    print("🔐 检测到认证问题，尝试重新登录...")
                    sess = sign_in()
                elif sim_resp.status_code >= 500:
                    # 服务器错误：通常是临时性问题
                    print("🔧 服务器错误，这通常是临时性问题")
                
                failure_count += 1

        except KeyError as e:
            # ==================== 响应头缺失处理 ====================
            
            # 当响应中缺少Location头时的处理
            error_message = f"⚠️ Alpha {alpha_index} 响应中缺少Location信息: {str(e)}"
            logging.error(error_message)
            print(error_message)
            print("   这可能表示服务器处理异常或API响应格式变更")
            
            failure_count += 1
            
        except requests.exceptions.Timeout:
            # ==================== 超时处理 ====================
            
            timeout_message = f"⏱️ Alpha {alpha_index} 请求超时，网络可能较慢"
            logging.error(timeout_message)
            print(timeout_message)
            
            failure_count += 1
            
        except requests.exceptions.ConnectionError as e:
            # ==================== 网络连接错误处理 ====================
            
            connection_error = f"🌐 Alpha {alpha_index} 网络连接错误: {str(e)}"
            logging.error(connection_error)
            print(connection_error)
            print("   尝试重新建立连接...")
            
            # 网络问题时重新登录以重建连接
            sess = sign_in()
            failure_count += 1
            
        except Exception as e:
            # ==================== 通用异常处理 ====================
            
            # 处理所有其他未预期的异常
            general_error = f"🚨 Alpha {alpha_index} 发生未预期异常: {type(e).__name__}: {str(e)}"
            logging.error(general_error)
            print(general_error)
            print("   尝试重新建立会话...")
            
            failure_count += 1
        
        # ==================== 重试逻辑和失败处理 ====================
        
        # 检查是否达到失败容忍上限
        if failure_count >= alpha_fail_attempt_tolerance:
            # 达到最大重试次数，放弃当前Alpha
            final_error = f"💔 Alpha {alpha_index} 达到最大重试次数 ({alpha_fail_attempt_tolerance})，放弃处理"
            logging.error(final_error)
            print(final_error)
            
            # 尝试重新登录会话，为下一个Alpha做准备
            print("   尝试重新登录以处理下一个Alpha...")
            sess = sign_in()
            
            # 重置失败计数器，继续处理下一个Alpha
            failure_count = 0
            failed_submissions += 1
            keep_trying = False  # 退出while循环，移动到下一个Alpha
            
            break  # 退出重试循环
        
        # 如果需要重试且未达到上限，添加延迟避免过于频繁的请求
        if keep_trying and not submission_successful:
            # 实施指数退避策略：重试间隔逐渐增加
            wait_time = min(2 ** (failure_count - 1), 15)  # 最大等待15秒
            print(f"   等待 {wait_time} 秒后进行第 {failure_count + 1} 次重试...")
            sleep(wait_time)
    
    # ==================== 单个Alpha处理完成 ====================
    
    completion_message = f"📊 Alpha {alpha_index} 处理完成"
    if submission_successful:
        completion_message += " - ✅ 成功"
    else:
        completion_message += " - ❌ 失败"
    
    print(completion_message)
    logging.info(completion_message)
    
    # 显示当前处理进度
    print(f"   当前进度: {total_processed}/{len(alpha_list)}")
    print(f"   成功: {successful_submissions}, 失败: {failed_submissions}")

# ==================== 批量提交总结报告 ====================

print(f"\n" + "="*60)
print(f"🏁 Alpha策略批量提交完成")
print(f"="*60)

# 生成详细的统计报告
print(f"\n📈 提交统计报告：")
print(f"   总处理数量: {total_processed} 个Alpha策略")
print(f"   成功提交: {successful_submissions} 个 ({successful_submissions/total_processed*100:.1f}%)")
print(f"   提交失败: {failed_submissions} 个 ({failed_submissions/total_processed*100:.1f}%)")

if total_processed > 0:
    success_rate = successful_submissions / total_processed * 100
    print(f"   整体成功率: {success_rate:.1f}%")

# 记录最终统计到日志
final_log = f"批量提交完成 - 总数:{total_processed}, 成功:{successful_submissions}, 失败:{failed_submissions}"
logging.info(final_log)

# 根据结果提供相应的后续建议
if successful_submissions > 0:
    print(f"\n✅ 成功提交的Alpha策略状态：")
    print(f"   - 已在WorldQuant Brain平台排队进行回测")
    print(f"   - 回测结果可在平台Simulations页面查看")
    print(f"   - 回测完成后将获得详细的性能指标和风险分析")
    print(f"   - 建议定期检查模拟进度和结果")

if failed_submissions > 0:
    print(f"\n⚠️ 失败Alpha策略的处理建议：")
    print(f"   1. 检查simulation.log文件获取详细错误信息")
    print(f"   2. 验证网络连接稳定性和API访问权限")
    print(f"   3. 确认Alpha表达式语法和数据字段有效性")
    print(f"   4. 考虑在网络状况良好时重新运行失败的策略")
    print(f"   5. 检查WorldQuant Brain平台是否有维护公告")

print(f"\n🎯 Alpha策略批量提交流程执行完毕！")
print(f"   详细日志已保存至: simulation.log")
print(f"   可通过日志文件追踪完整的提交过程和错误详情")