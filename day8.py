# 基于day7.py的完整功能，新增断点恢复和智能进度监控功能
# 完全继承Day7的所有基本功能，仅在批量处理函数中增加Day8的两个核心需求

import requests
import json
import os
import logging
import time
import pickle
import signal
import sys
from datetime import datetime, timedelta
from os.path import expanduser
from time import sleep
from requests.auth import HTTPBasicAuth
from typing import Dict, List, Tuple, Optional, Any

# ==================== Day7完整配置常量 ====================

# 文件路径配置（完全继承day7）
QUALIFIED_ALPHA_FILE = 'data/qualified_alphas.txt'
SIMULATION_LOG_FILE = 'logs/simulation.log'
CREDENTIALS_FILE = 'config/brain.txt'
DATASETS_CONFIG_FILE = 'config/datasets_config.json'
OPERATORS_CONFIG_FILE = 'config/operators_config.json'
COMBINATIONS_CONFIG_FILE = 'config/combinations_config.json'  # Day7新增

# Day8新增：断点恢复配置
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
CHECKPOINT_DIR = os.path.join(BASE_DIR, "checkpoints")
CHECKPOINT_FILE = os.path.join(CHECKPOINT_DIR, "session_checkpoint.pkl")
PROGRESS_STATS_FILE = os.path.join(CHECKPOINT_DIR, "progress_stats.json")

# 全局变量（Day8新增）
current_session = None
checkpoint_manager = None
progress_tracker = None
graceful_shutdown = False

# ==================== Day8新增：断点恢复和进度监控类 ====================

def setup_checkpoint_system():
    """初始化断点恢复系统"""
    if not os.path.exists(CHECKPOINT_DIR):
        os.makedirs(CHECKPOINT_DIR)
        print(f"✅ 创建断点恢复目录: {CHECKPOINT_DIR}")

def signal_handler(signum, frame):
    """优雅中断处理器（需求1）"""
    global graceful_shutdown
    print(f"\n\n🛑 收到中断信号 ({signum})，正在优雅关闭...")
    print("📝 正在保存当前进度到断点文件...")
    graceful_shutdown = True
    
    if checkpoint_manager:
        checkpoint_manager.save_checkpoint("用户中断")
        print("✅ 断点已保存，可使用恢复功能继续")
    
    print("👋 程序已安全退出")
    sys.exit(0)

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

class ProgressTracker:
    """智能进度追踪器（需求2）"""
    
    def __init__(self):
        self.api_response_times = []
        self.submission_times = []
        self.current_batch_start = None
        self.total_planned = 0
        self.completed = 0
        self.failed = 0
        
        # 智能估算参数
        self.avg_response_time = 3.0
        self.avg_processing_time = 15.0
        self.success_rate = 0.85
        
        self.load_progress_stats()
    
    def load_progress_stats(self):
        """加载历史进度统计数据"""
        try:
            if os.path.exists(PROGRESS_STATS_FILE):
                with open(PROGRESS_STATS_FILE, 'r', encoding='utf-8') as f:
                    stats = json.load(f)
                    self.avg_response_time = stats.get('avg_response_time', 3.0)
                    self.avg_processing_time = stats.get('avg_processing_time', 15.0)
                    self.success_rate = stats.get('success_rate', 0.85)
        except Exception as e:
            pass  # 使用默认值
    
    def save_progress_stats(self):
        """保存进度统计数据"""
        try:
            stats = {
                'avg_response_time': self.avg_response_time,
                'avg_processing_time': self.avg_processing_time,
                'success_rate': self.success_rate,
                'last_updated': datetime.now().isoformat()
            }
            with open(PROGRESS_STATS_FILE, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
        except Exception as e:
            pass
    
    def start_batch(self, total_count: int):
        """开始新的批次追踪"""
        self.total_planned = total_count
        self.completed = 0
        self.failed = 0
        self.current_batch_start = datetime.now()
        self.api_response_times = []
        self.submission_times = []
    
    def record_api_response(self, response_time: float):
        """记录API响应时间"""
        self.api_response_times.append(response_time)
        if len(self.api_response_times) >= 3:
            self.avg_response_time = sum(self.api_response_times[-10:]) / min(len(self.api_response_times), 10)
    
    def record_submission(self, processing_time: float, success: bool):
        """记录提交结果"""
        self.submission_times.append(processing_time)
        if success:
            self.completed += 1
        else:
            self.failed += 1
        
        if len(self.submission_times) >= 3:
            self.avg_processing_time = sum(self.submission_times[-10:]) / min(len(self.submission_times), 10)
            self.success_rate = self.completed / (self.completed + self.failed)
    
    def display_progress(self, current_index: int):
        """显示智能进度信息"""
        if self.total_planned == 0:
            return
            
        progress_pct = min((current_index / self.total_planned) * 100, 99)
        
        # 构建进度条
        bar_length = 30
        filled_length = int(bar_length * progress_pct / 100)
        bar = '█' * filled_length + '░' * (bar_length - filled_length)
        
        # ETA计算
        remaining = self.total_planned - current_index
        estimated_time_per_alpha = self.avg_response_time + self.avg_processing_time
        eta_seconds = remaining * estimated_time_per_alpha
        
        if eta_seconds < 60:
            eta_formatted = f"{eta_seconds:.0f}秒"
        elif eta_seconds < 3600:
            eta_formatted = f"{eta_seconds/60:.0f}分钟"
        else:
            hours = eta_seconds // 3600
            minutes = (eta_seconds % 3600) // 60
            eta_formatted = f"{hours:.0f}小时{minutes:.0f}分钟"
        
        print(f"📊 智能进度监控: [{bar}] {progress_pct:.1f}%")
        print(f"   完成: {self.completed}/{self.total_planned} (成功率: {self.success_rate:.1%})")
        print(f"   预计剩余: {eta_formatted}")
    
    def finish_batch(self):
        """完成批次处理"""
        if self.current_batch_start:
            total_time = (datetime.now() - self.current_batch_start).total_seconds()
            self.save_progress_stats()

class CheckpointManager:
    """断点管理器（需求1）"""
    
    def __init__(self):
        self.checkpoint_data = {}
        self.last_save_time = None
        
    def save_checkpoint(self, reason: str = "定期保存"):
        """保存检查点"""
        try:
            global current_session, progress_tracker
            
            checkpoint_data = {
                'timestamp': datetime.now().isoformat(),
                'reason': reason,
                'session_valid': current_session is not None,
                'progress_data': {
                    'total_planned': progress_tracker.total_planned if progress_tracker else 0,
                    'completed': progress_tracker.completed if progress_tracker else 0,
                    'failed': progress_tracker.failed if progress_tracker else 0,
                } if progress_tracker else {},
            }
            
            with open(CHECKPOINT_FILE, 'wb') as f:
                pickle.dump(checkpoint_data, f)
            
            self.last_save_time = datetime.now()
            
        except Exception as e:
            print(f"❌ 保存检查点失败: {e}")
    
    def load_checkpoint(self) -> Optional[Dict]:
        """加载检查点"""
        try:
            if not os.path.exists(CHECKPOINT_FILE):
                return None
                
            with open(CHECKPOINT_FILE, 'rb') as f:
                checkpoint_data = pickle.load(f)
            
            # 检查检查点是否过期（24小时）
            checkpoint_time = datetime.fromisoformat(checkpoint_data['timestamp'])
            if datetime.now() - checkpoint_time > timedelta(hours=24):
                return None
                
            return checkpoint_data
            
        except Exception as e:
            return None
    
    def clear_checkpoint(self):
        """清除检查点文件"""
        try:
            if os.path.exists(CHECKPOINT_FILE):
                os.remove(CHECKPOINT_FILE)
        except Exception as e:
            pass
    
    def should_save_checkpoint(self, completed_count: int) -> bool:
        """判断是否应该保存检查点（每10个Alpha保存一次）"""
        if completed_count % 10 == 0 and completed_count > 0:
            return True
        if self.last_save_time is None:
            return True
        if datetime.now() - self.last_save_time > timedelta(minutes=10):
            return True
        return False

# ==================== 完全继承Day7的所有配置加载函数 ====================

def load_datasets_config():
    """加载数据集配置（继承自day7.py）"""
    try:
        with open(DATASETS_CONFIG_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 找不到数据集配置文件: {DATASETS_CONFIG_FILE}")
        return None
    except json.JSONDecodeError:
        print(f"❌ 数据集配置文件格式错误: {DATASETS_CONFIG_FILE}")
        return None

def load_operators_config():
    """加载运算符配置（继承自day7.py）"""
    try:
        with open(OPERATORS_CONFIG_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 找不到运算符配置文件: {OPERATORS_CONFIG_FILE}")
        return None
    except json.JSONDecodeError:
        print(f"❌ 运算符配置文件格式错误: {OPERATORS_CONFIG_FILE}")
        return None

def load_combinations_config():
    """加载组合配置（Day7新增功能）"""
    try:
        with open(COMBINATIONS_CONFIG_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 找不到组合配置文件: {COMBINATIONS_CONFIG_FILE}")
        return None
    except json.JSONDecodeError:
        print(f"❌ 组合配置文件格式错误: {COMBINATIONS_CONFIG_FILE}")
        return None

# ==================== 完全继承Day7的所有管理类 ====================

class DatasetManager:
    """数据集管理器（完全继承自day7.py）"""
    
    def __init__(self):
        self.config = load_datasets_config()
        if not self.config:
            raise Exception("无法加载数据集配置")
        self.datasets = self.config['datasets']
        self.default_config = self.config['default_config']
    
    def list_all_datasets(self):
        """列出所有可用数据集（继承自day7.py）"""
        print(f"\n📊 可用数据集列表 (共{len(self.datasets)}个):")
        print("="*80)
        
        for i, (dataset_id, config) in enumerate(self.datasets.items(), 1):
            print(f"{i:2d}. {dataset_id:<15} - {config['name']}")
            print(f"    类别: {config['category']}")
            print(f"    覆盖率: {config['coverage']:<4} | 字段数: {config['field_count']:<3} | 描述: {config['description'][:60]}...")
            print()
    
    def get_dataset_by_choice(self, choice):
        """根据用户选择获取数据集配置（继承自day7.py）"""
        try:
            choice_num = int(choice)
            dataset_ids = list(self.datasets.keys())
            
            if 1 <= choice_num <= len(dataset_ids):
                dataset_id = dataset_ids[choice_num - 1]
                config = self.datasets[dataset_id]
                
                best_universe = self._select_best_universe(config['universes'])
                best_delay = str(config['delays'][0])
                
                search_scope = {
                    'instrumentType': self.default_config['instrumentType'],
                    'region': config['regions'][0],
                    'delay': best_delay,
                    'universe': best_universe
                }
                
                return dataset_id, search_scope, config
            else:
                return None, None, None
                
        except ValueError:
            return None, None, None
    
    def _select_best_universe(self, universes):
        """选择最佳股票池（继承自day7.py）"""
        priority = self.default_config['universe_priority']
        for universe in priority:
            if universe in universes:
                return universe
        return universes[0] if universes else 'TOP1000'
    
    def get_dataset_info(self, dataset_id):
        """获取数据集详细信息（继承自day7.py）"""
        return self.datasets.get(dataset_id, None)

class OperatorLibrary:
    """运算符库管理器（完全继承自day7.py）"""
    
    def __init__(self):
        self.config = load_operators_config()
        if not self.config:
            raise Exception("无法加载运算符配置")
        self.operators = self.config['operator_categories']
        self.operator_sets = self.config['operator_sets']
    
    def get_operator_set(self, set_name='basic'):
        """获取运算符组合（继承自day7.py）"""
        return self.operator_sets.get(set_name, self.operator_sets['basic'])

class CombinationManager:
    """运算符组合管理器（Day7新增类）"""
    
    def __init__(self):
        self.config = load_combinations_config()
        if not self.config:
            raise Exception("无法加载组合配置")
        self.combination_sets = self.config['combination_sets']
        self.custom_combinations = self.config['custom_combinations']
        self.metadata = self.config['combination_metadata']
    
    def list_all_combinations(self):
        """列出所有可用组合"""
        print(f"\n🔧 可用运算符组合列表 (共{len(self.combination_sets)}个预设组合):")
        print("="*80)
        
        for i, (combo_id, config) in enumerate(self.combination_sets.items(), 1):
            print(f"{i:2d}. {config['name']}")
            print(f"    描述: {config['description']}")
            print(f"    分组运算符: {config['group_ops']}")
            print(f"    时间序列: {config['ts_ops']}")
            print(f"    周期: {config['periods']}")
            print()
    
    def get_combination_by_choice(self, choice):
        """根据用户选择获取组合配置"""
        try:
            choice_num = int(choice)
            combo_ids = list(self.combination_sets.keys())
            
            if 1 <= choice_num <= len(combo_ids):
                combo_id = combo_ids[choice_num - 1]
                config = self.combination_sets[combo_id]
                return combo_id, config
            else:
                return None, None
                
        except ValueError:
            return None, None
    
    def get_combination_info(self, combo_id):
        """获取组合详细信息"""
        return self.combination_sets.get(combo_id, None)

# ==================== 完全继承Day7的API认证和数据获取函数 ====================

def sign_in():
    """WorldQuant Brain API认证函数（完全继承自day7.py）"""
    global current_session
    
    try:
        # Day8修改：从环境变量获取认证信息，兼容brain.txt
        username = os.getenv('WQ_USERNAME')
        password = os.getenv('WQ_PASSWORD')
        
        if not username or not password:
            # 如果环境变量没设置，尝试从brain.txt读取
            try:
                with open(expanduser(CREDENTIALS_FILE)) as f:
                    credentials = json.load(f)
                username, password = credentials
            except:
                print("❌ 环境变量 WQ_USERNAME 或 WQ_PASSWORD 未设置")
                print("请设置这些环境变量后重试")
                sys.exit(1)
        
        sess = requests.Session()
        sess.auth = HTTPBasicAuth(username, password)
        
        response = sess.post('https://api.worldquantbrain.com/authentication')
        
        if response.status_code == 201:
            print("✅ WorldQuant Brain API认证成功")
            current_session = sess
        else:
            print(f"❌ 认证失败，状态码: {response.status_code}")
        
        return sess
        
    except Exception as e:
        print(f"❌ 认证过程发生错误: {str(e)}")
        raise

def get_datafields(s, searchScope, dataset_id='', search=''):
    """获取WorldQuant Brain数据字段信息（完全继承自day7.py）"""
    import pandas as pd
    
    instrument_type = searchScope['instrumentType']
    region = searchScope['region']
    delay = searchScope['delay']
    universe = searchScope['universe']
    
    print(f"🔍 开始获取数据字段...")
    print(f"   数据集ID: {dataset_id if dataset_id else '全部'}")
    print(f"   市场范围: {region} {universe}")
    
    if len(search) == 0:
        url_template = "https://api.worldquantbrain.com/data-fields?" + \
                       f"&instrumentType={instrument_type}" + \
                       f"&region={region}&delay={str(delay)}&universe={universe}&dataset.id={dataset_id}&limit=50" + \
                       "&offset={x}"
        
        initial_response = s.get(url_template.format(x=0))
        if initial_response.status_code == 200:
            count = initial_response.json()['count']
            print(f"   总记录数: {count}")
            
            # Day8新增：记录API响应时间
            if progress_tracker:
                progress_tracker.record_api_response(0.5)  # 估算值
        else:
            print(f"❌ 获取记录数失败，状态码: {initial_response.status_code}")
            count = 0
    else:
        url_template = "https://api.worldquantbrain.com/data-fields?" + \
                       f"&instrumentType={instrument_type}" + \
                       f"&region={region}&delay={str(delay)}&universe={universe}&limit=50" + \
                       f"&search={search}" + \
                       "&offset={x}"
        count = 100
    
    datafields_list = []
    pages_processed = 0
    
    for x in range(0, count, 50):
        try:
            start_time = time.time()
            datafields = s.get(url_template.format(x=x))
            response_time = time.time() - start_time
            
            # Day8新增：记录API响应时间
            if progress_tracker:
                progress_tracker.record_api_response(response_time)
            
            if datafields.status_code == 200:
                results = datafields.json()['results']
                datafields_list.append(results)
                pages_processed += 1
                print(f"   已处理第 {pages_processed} 页，获取 {len(results)} 条记录")
            else:
                print(f"❌ 第 {pages_processed + 1} 页请求失败，状态码: {datafields.status_code}")
                break
        except Exception as e:
            print(f"❌ 处理第 {pages_processed + 1} 页时发生错误: {e}")
            break
    
    datafields_list_flat = [item for sublist in datafields_list for item in sublist]
    datafields_df = pd.DataFrame(datafields_list_flat)
    
    print(f"✅ 数据字段获取完成，共 {len(datafields_df)} 条记录")
    return datafields_df

# ==================== 完全继承Day7的数据集选择和组合选择功能 ====================

def choose_by_category(dataset_manager):
    """按7大类选择数据集（继承自day7.py）"""
    
    # 基于datasets_config.json的category字段分类
    CATEGORIES = {
        1: {'name': '📊 分析师研究', 'pattern': 'Analyst'},
        2: {'name': '💰 基本面分析', 'pattern': 'Fundamental'}, 
        3: {'name': '🤖 量化模型', 'pattern': 'Model'},
        4: {'name': '📰 新闻事件', 'pattern': 'News'},
        5: {'name': '📈 期权市场', 'pattern': 'Option'},
        6: {'name': '💹 价格成交量', 'pattern': 'Price Volume'},
        7: {'name': '📱 社交媒体', 'pattern': 'Social Media'}
    }
    
    print("\n📊 按数据类别选择:")
    print("="*50)
    for num, info in CATEGORIES.items():
        print(f"{num}. {info['name']}")
    
    category_choice = input("\n请选择数据类别 (1-7): ").strip()
    
    try:
        category_num = int(category_choice)
        if 1 <= category_num <= 7:
            selected_category = CATEGORIES[category_num]
            
            # 找到该类别下的所有数据集
            matching_datasets = []
            for i, (dataset_id, config) in enumerate(dataset_manager.datasets.items(), 1):
                if selected_category['pattern'] in config['category']:
                    matching_datasets.append((i, dataset_id, config))
            
            if not matching_datasets:
                print(f"❌ 该类别下没有可用的数据集")
                return None, None, None
            
            print(f"\n✅ {selected_category['name']} 类别下的数据集:")
            print("-" * 60)
            for j, (original_num, dataset_id, config) in enumerate(matching_datasets, 1):
                print(f"{j}. {dataset_id} - {config['name']}")
                print(f"   覆盖率: {config['coverage']:<4} | 字段数: {config['field_count']:<3}")
                print()
            
            dataset_choice = input(f"请选择数据集 (1-{len(matching_datasets)}): ").strip()
            
            try:
                choice_num = int(dataset_choice)
                if 1 <= choice_num <= len(matching_datasets):
                    selected_idx = matching_datasets[choice_num - 1][0]  # 获取原始序号
                    return dataset_manager.get_dataset_by_choice(str(selected_idx))
                else:
                    print("❌ 无效选择")
                    return None, None, None
            except ValueError:
                print("❌ 请输入有效数字")
                return None, None, None
        else:
            print("❌ 无效的类别选择")
            return None, None, None
    except ValueError:
        print("❌ 请输入有效数字")
        return None, None, None

def choose_combination_mode(combination_manager):
    """选择运算符组合模式（Day7新增功能）"""
    
    print("\n🔧 请选择运算符组合模式:")
    print("="*50)
    print("1. 经典组合模式 - 使用预设的经典运算符组合")
    print("2. 批量组合模式 - 连续测试多个组合（推荐）")
    print("3. 自定义模式 - 手动选择运算符（回退到Day6方式）")
    
    mode_choice = input("\n请选择模式 (1-3): ").strip()
    
    if mode_choice == '1':
        return choose_single_combination(combination_manager)
    elif mode_choice == '2':
        return choose_batch_combinations(combination_manager)
    elif mode_choice == '3':
        return None  # 回退到原有的basic/advanced选择
    else:
        print("❌ 无效选择")
        return None

def choose_single_combination(combination_manager):
    """选择单个经典组合"""
    
    combination_manager.list_all_combinations()
    
    combo_choice = input(f"\n请选择组合 (1-{len(combination_manager.combination_sets)}): ").strip()
    
    combo_id, combo_config = combination_manager.get_combination_by_choice(combo_choice)
    
    if not combo_id:
        print("❌ 无效的组合选择")
        return None
    
    print(f"\n✅ 已选择组合: {combo_config['name']}")
    print(f"   描述: {combo_config['description']}")
    print(f"   分组运算符: {combo_config['group_ops']}")
    print(f"   时间序列运算符: {combo_config['ts_ops']}")
    print(f"   时间周期: {combo_config['periods']}")
    print(f"   分组方法: {combo_config['groups']}")
    
    return [combo_config]  # 返回单个组合的列表

def choose_batch_combinations(combination_manager):
    """选择多个组合进行批量测试"""
    
    print("\n🚀 批量组合模式 - 推荐组合:")
    print("="*60)
    print("1. 快速测试 - 3个经典组合 [趋势跟踪, 均值回归, 动量策略]")
    print("2. 全面测试 - 5个主要组合 [趋势跟踪, 均值回归, 动量策略, 波动性策略, 保守策略]")
    print("3. 完整测试 - 全部7个组合")
    print("4. 自定义选择 - 手动选择要测试的组合")
    
    batch_choice = input("\n请选择批量模式 (1-4): ").strip()
    
    if batch_choice == '1':
        # 快速测试：3个经典组合
        selected_combos = ['trend_following', 'mean_reversion', 'momentum']
    elif batch_choice == '2':
        # 全面测试：5个主要组合
        selected_combos = ['trend_following', 'mean_reversion', 'momentum', 'volatility_based', 'conservative']
    elif batch_choice == '3':
        # 完整测试：全部组合
        selected_combos = list(combination_manager.combination_sets.keys())
    elif batch_choice == '4':
        # 自定义选择
        return choose_custom_batch_combinations(combination_manager)
    else:
        print("❌ 无效选择")
        return None
    
    # 获取选中组合的配置
    batch_configs = []
    for combo_id in selected_combos:
        if combo_id in combination_manager.combination_sets:
            config = combination_manager.combination_sets[combo_id]
            config['id'] = combo_id  # 添加ID用于标识
            batch_configs.append(config)
    
    print(f"\n✅ 已选择 {len(batch_configs)} 个组合进行批量测试:")
    for i, config in enumerate(batch_configs, 1):
        print(f"   {i}. {config['name']} - {config['description'][:40]}...")
    
    return batch_configs

def choose_custom_batch_combinations(combination_manager):
    """自定义选择多个组合"""
    
    combination_manager.list_all_combinations()
    
    print("\n请输入要测试的组合序号，用逗号分隔 (例如: 1,2,5):")
    choices_input = input("组合序号: ").strip()
    
    try:
        choice_nums = [int(x.strip()) for x in choices_input.split(',')]
        combo_ids = list(combination_manager.combination_sets.keys())
        
        selected_configs = []
        for choice_num in choice_nums:
            if 1 <= choice_num <= len(combo_ids):
                combo_id = combo_ids[choice_num - 1]
                config = combination_manager.combination_sets[combo_id]
                config['id'] = combo_id
                selected_configs.append(config)
            else:
                print(f"❌ 无效序号: {choice_num}")
        
        if selected_configs:
            print(f"\n✅ 已选择 {len(selected_configs)} 个组合:")
            for i, config in enumerate(selected_configs, 1):
                print(f"   {i}. {config['name']}")
            return selected_configs
        else:
            print("❌ 没有选择有效的组合")
            return None
            
    except ValueError:
        print("❌ 输入格式错误，请使用数字和逗号")
        return None

def generate_alpha_expressions_with_combination(datafields_list, combination_config):
    """使用指定组合配置生成Alpha表达式（Day7新增）"""
    
    combo_name = combination_config.get('name', '未知组合')
    print(f"\n🔧 使用组合配置生成Alpha表达式: {combo_name}")
    print(f"   分组操作符: {len(combination_config['group_ops'])} 种 - {combination_config['group_ops']}")
    print(f"   时间序列操作符: {len(combination_config['ts_ops'])} 种 - {combination_config['ts_ops']}")
    print(f"   基本面字段: {len(datafields_list)} 个")
    print(f"   时间周期: {combination_config['periods']} 天")
    print(f"   分组方法: {len(combination_config['groups'])} 种 - {combination_config['groups']}")
    
    alpha_expressions = []
    expression_count = 0
    
    total_combinations = (len(combination_config['group_ops']) * 
                        len(combination_config['ts_ops']) * 
                        len(datafields_list) * 
                        len(combination_config['periods']) * 
                        len(combination_config['groups']))
    print(f"   预期生成表达式总数: {total_combinations:,} 个")
    
    for gco in combination_config['group_ops']:
        for tco in combination_config['ts_ops']:
            for cf in datafields_list:
                for d in combination_config['periods']:
                    for grp in combination_config['groups']:
                        alpha_expression = f"{gco}({tco}({cf}, {d}), {grp})"
                        alpha_expressions.append(alpha_expression)
                        expression_count += 1
                        
                        if expression_count % 500 == 0:
                            print(f"   已生成 {expression_count:,} 个表达式...")
    
    print(f"\n✅ 组合 '{combo_name}' Alpha表达式生成完成！")
    print(f"   实际生成表达式总数: {len(alpha_expressions):,} 个")
    print(f"   表达式示例:")
    for i, expr in enumerate(alpha_expressions[:3], 1):
        print(f"     {i}. {expr}")
    
    return alpha_expressions

# ==================== Day7完整质量检测和会话管理功能 ====================

def check_alpha_quality(alpha_data):
    """检查Alpha是否符合7个核心质量标准（完全继承自day7.py）"""
    is_data = alpha_data.get('is', {})
    
    # 提取关键指标
    sharpe = is_data.get('sharpe', 0)
    fitness = is_data.get('fitness', 0)
    turnover = is_data.get('turnover', 0) * 100  # 转换为百分比
    sub_universe_sharpe = is_data.get('subUniverseSharpe', 0)
    weight_concentration = is_data.get('maxWeight', 0) * 100
    ic_mean = is_data.get('ic_mean', 0)
    returns = is_data.get('returns', 0)
    
    # 7个核心质量标准检查
    checks = {
        'turnover_above_min': {
            'passed': turnover >= 1.0,
            'message': f"Turnover of {turnover:.2f}% is {'above' if turnover >= 1.0 else 'below'} cutoff of 1%.",
            'status': 'PASS' if turnover >= 1.0 else 'FAIL'
        },
        'turnover_below_max': {
            'passed': turnover <= 70.0,
            'message': f"Turnover of {turnover:.2f}% is {'below' if turnover <= 70.0 else 'above'} cutoff of 70%.",
            'status': 'PASS' if turnover <= 70.0 else 'FAIL'
        },
        'sub_universe_sharpe_ok': {
            'passed': sub_universe_sharpe >= -1.54,
            'message': f"Sub-universe Sharpe of {sub_universe_sharpe:.2f} is {'above' if sub_universe_sharpe >= -1.54 else 'below'} cutoff of -1.54.",
            'status': 'PASS' if sub_universe_sharpe >= -1.54 else 'FAIL'
        },
        'competition_challenge': {
            'passed': True,
            'message': "Competition Challenge matches.",
            'status': 'PASS'
        },
        'sharpe_ok': {
            'passed': sharpe >= 1.25,
            'message': f"Sharpe of {sharpe:.2f} is {'above' if sharpe >= 1.25 else 'below'} cutoff of 1.25.",
            'status': 'PASS' if sharpe >= 1.25 else 'FAIL'
        },
        'fitness_ok': {
            'passed': fitness >= 1.0,
            'message': f"Fitness of {fitness:.2f} is {'above' if fitness >= 1.0 else 'below'} cutoff of 1.",
            'status': 'PASS' if fitness >= 1.0 else 'FAIL'
        },
        'weight_concentration_ok': {
            'passed': weight_concentration <= 10.0,
            'message': f"Weight concentration {weight_concentration:.0f}% is {'above' if weight_concentration <= 10.0 else 'below'} cutoff of 10% on {datetime.now().strftime('%m/%d/%Y')}.",
            'status': 'PASS' if weight_concentration <= 10.0 else 'FAIL'
        }
    }
    
    passed_checks = sum(1 for check in checks.values() if check['passed'])
    total_checks = len(checks)
    is_qualified = passed_checks == 7
    
    result = {
        'qualified': is_qualified,
        'passed_checks': passed_checks,
        'total_checks': total_checks,
        'metrics': {
            'sharpe': sharpe,
            'fitness': fitness,
            'turnover': turnover,
            'sub_universe_sharpe': sub_universe_sharpe,
            'weight_concentration': weight_concentration,
            'ic_mean': ic_mean,
            'returns': returns
        },
        'checks': checks
    }
    
    return is_qualified, result

def check_session_validity(sess):
    """检查会话有效性（Day7新增功能）"""
    try:
        test_response = sess.get('https://api.worldquantbrain.com/data-fields?limit=1')
        if test_response.status_code in [401, 403]:
            return False
        return True
    except Exception:
        return False

def maintain_session_health(sess, context="unknown"):
    """维护会话健康状态（Day7新增功能）"""
    try:
        if not check_session_validity(sess):
            print(f"\n🔴 在{context}操作中检测到会话已失效")
            print(f"   🔄 正在重新建立会话...")
            new_sess = sign_in()
            print(f"   ✅ 会话已重新建立，可以继续{context}操作")
            return new_sess
        return sess
    except Exception as e:
        print(f"   ⚠️ 维护会话健康时发生错误: {str(e)}")
        return sess

def create_alpha_list(alpha_expressions, searchScope):
    """将Alpha表达式封装为WorldQuant Brain模拟请求格式（完全继承自day7.py）"""
    print("\n🎯 开始Alpha策略封装和模拟准备阶段")
    print("="*60)
    print("\n📦 将Alpha表达式封装为WorldQuant Brain模拟请求格式...")
    
    alpha_list = []
    
    for index, alpha_expression in enumerate(alpha_expressions, start=1):
        if index <= 5:  # 只显示前5个的详细信息
            print(f"📝 正在处理第 {index} 个Alpha表达式...")
            print(f"   表达式: {alpha_expression}")
        
        simulation_data = {
            "type": "REGULAR",
            "settings": {
                "instrumentType": searchScope['instrumentType'],
                "region": searchScope['region'],
                "universe": searchScope['universe'],
                "delay": int(searchScope['delay']),
                "decay": 0,
                "neutralization": "SUBINDUSTRY",
                "truncation": 0.01,
                "pasteurization": "ON",
                "unitHandling": "VERIFY",
                "nanHandling": "OFF",
                "language": "FASTEXPR",
                "visualization": False,
            },
            "regular": alpha_expression
        }
        
        alpha_list.append(simulation_data)
        
        if index % 100 == 0 or index <= 5:
            print(f"   ✅ 已封装 {len(alpha_list)} 个Alpha策略")
    
    print(f"\n📄 Alpha策略封装完成统计：")
    print(f"   总策略数量: {len(alpha_list):,} 个")
    print(f"   封装格式: WorldQuant Brain标准模拟请求")
    print(f"   目标市场: {searchScope['region']} {searchScope['universe']}")
    print(f"   中性化方法: 子行业中性")
    
    if alpha_list:
        print(f"\n🔍 封装示例 (第1个Alpha策略):")
        print(f"   Alpha表达式: {alpha_list[0]['regular']}")
    
    return alpha_list

def setup_logging():
    """配置日志系统（完全继承自day7.py）"""
    logging.basicConfig(
        filename=SIMULATION_LOG_FILE,
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        filemode='a'
    )
    
    # 创建自定义日志记录器
    detailed_logger = logging.getLogger('alpha_details')
    detailed_handler = logging.FileHandler('logs/alpha_test_results.log', mode='a', encoding='utf-8')
    detailed_formatter = logging.Formatter('%(asctime)s - %(message)s')
    detailed_handler.setFormatter(detailed_formatter)
    detailed_logger.addHandler(detailed_handler)
    detailed_logger.setLevel(logging.INFO)
    
    return detailed_logger

def save_qualified_alpha(alpha_id, alpha_expression, quality_result, alpha_index=None, combination_name=None):
    """保存合格Alpha ID到文件（完全继承Day7）"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 准备保存的数据（包含完整的检测结果和序号）
    alpha_record = {
        'alpha_id': alpha_id,
        'expression': alpha_expression,
        'alpha_index': alpha_index,  # 添加序号
        'combination_name': combination_name,  # Day7增加组合名称
        'timestamp': timestamp,
        'qualified': quality_result['qualified'],
        'passed_checks': quality_result['passed_checks'],
        'total_checks': quality_result['total_checks'],
        'metrics': quality_result['metrics'],
        'detailed_checks': quality_result['checks']
    }
    
    # 追加到合格 Alpha文件
    with open(QUALIFIED_ALPHA_FILE, 'a', encoding='utf-8') as f:
        f.write(json.dumps(alpha_record, ensure_ascii=False) + '\n')
    
    # 同时保存到专门的成功Alpha ID文件（包含序号）
    success_alpha_file = 'data/successful_alpha_ids.txt'
    
    # 如果文件不存在，先写入表头
    if not os.path.exists(success_alpha_file):
        with open(success_alpha_file, 'w', encoding='utf-8') as f:
            f.write("Alpha_ID\t序号\tAlpha表达式\t组合名称\t时间戳\t通过标准\n")
    
    with open(success_alpha_file, 'a', encoding='utf-8') as f:
        index_str = f"第{alpha_index}个" if alpha_index else "未知序号"
        combo_str = combination_name if combination_name else "未知组合"
        f.write(f"{alpha_id}\t{index_str}\t{alpha_expression}\t{combo_str}\t{timestamp}\t{quality_result['passed_checks']}/{quality_result['total_checks']}\n")
    
    print(f"💾 已保存合格Alpha: {alpha_id} ({index_str})")
    if combination_name:
        print(f"   组合名称: {combination_name}")
    print(f"   详细记录: {QUALIFIED_ALPHA_FILE}")
    print(f"   ID列表: {success_alpha_file}")
    
    # 特别提醒：这是完全通过的Alpha
    if quality_result['passed_checks'] == 7:
        print(f"🏆 重要发现！Alpha {alpha_id} 完全通过所有7个标准！")
        print(f"   这是第 {alpha_index} 个测试的Alpha，请重点关注！")

def display_alpha_result(alpha_id, alpha_expression, quality_result, combination_name=None):
    """显示Alpha测试结果（完全继承Day7）"""
    print(f"\n" + "="*60)
    print(f"📄 Alpha测试结果 - ID: {alpha_id}")
    if combination_name:
        print(f"🔧 组合名称: {combination_name}")
    print(f"="*60)
    print(f"表达式: {alpha_expression}")
    print(f"质量评级: {'✅ 合格' if quality_result['qualified'] else '❌ 不合格'}")
    print(f"通过检查: {quality_result['passed_checks']}/{quality_result['total_checks']}")
    
    print(f"\n📋 IS Testing Status (IS 测试状态)")
    
    # 统计PASS和FAIL数量
    pass_count = sum(1 for check in quality_result['checks'].values() if check['status'] == 'PASS')
    fail_count = sum(1 for check in quality_result['checks'].values() if check['status'] == 'FAIL')
    
    print(f"\n✅ {pass_count} PASS ({pass_count} 及格)")
    for check_name, check_info in quality_result['checks'].items():
        if check_info['status'] == 'PASS':
            print(f"   {check_info['message']}")
    
    if fail_count > 0:
        print(f"\n❌ {fail_count} FAIL ({fail_count} 不及格)")
        for check_name, check_info in quality_result['checks'].items():
            if check_info['status'] == 'FAIL':
                print(f"   {check_info['message']}")
    
    print(f"\n📈 详细性能指标:")
    metrics = quality_result['metrics']
    print(f"   Sharpe: {metrics['sharpe']:.2f}")
    print(f"   Fitness: {metrics['fitness']:.2f}")
    print(f"   Turnover: {metrics['turnover']:.2f}%")
    print(f"   Sub-universe Sharpe: {metrics['sub_universe_sharpe']:.2f}")
    print(f"   Weight Concentration: {metrics['weight_concentration']:.0f}%")
    print(f"   IC Mean: {metrics['ic_mean']:.4f}")
    print(f"   Returns: {metrics['returns']:.4f}")
    print(f"\n" + "="*60)

def log_alpha_result(alpha_id, alpha_expression, quality_result, detailed_logger, combination_name=None):
    """记录Alpha测试结果到详细日志文件（完全继承Day7）"""
    log_content = f"""
{'='*60}
Alpha测试结果 - ID: {alpha_id}
{'='*60}
表达式: {alpha_expression}"""
    
    if combination_name:
        log_content += f"\n组合名称: {combination_name}"
    
    log_content += f"""
质量评级: {'合格' if quality_result['qualified'] else '不合格'}
通过检查: {quality_result['passed_checks']}/{quality_result['total_checks']}

IS Testing Status (IS 测试状态)
"""
    
    # 统计PASS和FAIL数量
    pass_count = sum(1 for check in quality_result['checks'].values() if check['status'] == 'PASS')
    fail_count = sum(1 for check in quality_result['checks'].values() if check['status'] == 'FAIL')
    
    log_content += f"\n{pass_count} PASS ({pass_count} 及格)\n"
    for check_name, check_info in quality_result['checks'].items():
        if check_info['status'] == 'PASS':
            log_content += f"   {check_info['message']}\n"
    
    if fail_count > 0:
        log_content += f"\n{fail_count} FAIL ({fail_count} 不及格)\n"
        for check_name, check_info in quality_result['checks'].items():
            if check_info['status'] == 'FAIL':
                log_content += f"   {check_info['message']}\n"
    
    log_content += f"\n详细性能指标:\n"
    metrics = quality_result['metrics']
    log_content += f"   Sharpe: {metrics['sharpe']:.2f}\n"
    log_content += f"   Fitness: {metrics['fitness']:.2f}\n"
    log_content += f"   Turnover: {metrics['turnover']:.2f}%\n"
    log_content += f"   Sub-universe Sharpe: {metrics['sub_universe_sharpe']:.2f}\n"
    log_content += f"   Weight Concentration: {metrics['weight_concentration']:.0f}%\n"
    log_content += f"   IC Mean: {metrics['ic_mean']:.4f}\n"
    log_content += f"   Returns: {metrics['returns']:.4f}\n"
    log_content += f"{'='*60}\n"
    
    detailed_logger.info(log_content)

def view_qualified_alphas():
    """查看已保存的合格Alpha策略（完全继承Day7）"""
    if not os.path.exists(QUALIFIED_ALPHA_FILE):
        print("📝 暂无保存的合格Alpha策略")
        return
    
    print("\n📋 已保存的合格Alpha策略:")
    print("="*60)
    
    try:
        with open(QUALIFIED_ALPHA_FILE, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if not lines:
            print("📝 暂无保存的合格Alpha策略")
            return
        
        for i, line in enumerate(lines, 1):
            try:
                alpha_record = json.loads(line.strip())
                print(f"\n{i}. Alpha ID: {alpha_record['alpha_id']}")
                print(f"   表达式: {alpha_record['expression']}")
                if 'combination_name' in alpha_record and alpha_record['combination_name']:
                    print(f"   组合名称: {alpha_record['combination_name']}")
                print(f"   保存时间: {alpha_record['timestamp']}")
                print(f"   通过检查: {alpha_record['passed_checks']}/{alpha_record['total_checks']}")
                
                metrics = alpha_record['metrics']
                print(f"   夏普比率: {metrics['sharpe']:.3f} | 适应度: {metrics['fitness']:.3f}")
                
            except json.JSONDecodeError:
                print(f"   ❌ 第{i}行数据格式错误")
                
    except Exception as e:
        print(f"❌ 读取文件时发生错误: {str(e)}")

def batch_submit_alphas_with_day8_enhancements(sess, alpha_list, combination_name=None, test_mode=True):
    """
    Day8增强版批量提交Alpha策略（完全继承Day7业务流程 + 增加断点恢复和智能进度监控）
    
    完整继承Day7的核心功能：
    - 完整的日志记录系统（双重日志）
    - 完整的7标准质量检测流程
    - 完整的Alpha详情获取和保存机制
    - 完整的重试和会话管理机制
    
    Day8新增功能：
    - 断点恢复机制
    - 智能进度监控
    """
    global checkpoint_manager, progress_tracker, graceful_shutdown
    
    # Day8新增：检查是否有断点可恢复
    checkpoint_data = checkpoint_manager.load_checkpoint() if checkpoint_manager else None
    start_index = 0
    
    if checkpoint_data:
        print(f"\n💾 发现断点文件: {checkpoint_data['timestamp']}")
        print(f"   原因: {checkpoint_data['reason']}")
        
        resume_choice = input("是否从断点继续？ (y/N): ").strip().lower()
        if resume_choice == 'y':
            progress_data = checkpoint_data.get('progress_data', {})
            start_index = progress_data.get('completed', 0) + progress_data.get('failed', 0)
            print(f"✅ 从第 {start_index + 1} 个Alpha开始恢复")
        else:
            checkpoint_manager.clear_checkpoint()
            print("🎆 开始全新处理")
    
    combo_display = f"🔧 [{combination_name}]" if combination_name else ""
    print(f"\n🚀 Day8增强版批量提交Alpha策略 {combo_display}")
    print("✨ 完整继承Day7业务流程 + 断点恢复 + 智能进度监控")
    print("="*60)
    
    # 初始化统计变量（完全继承Day7）
    alpha_fail_attempt_tolerance = 15  # Day7的重试次数
    total_processed = 0
    successful_submissions = 0
    failed_submissions = 0
    qualified_alphas = []
    
    # 根据模式决定处理数量（完全继承Day7）
    if test_mode:
        max_test_count = min(5, len(alpha_list))
        print(f"\n🧪 测试模式：只处理前 {max_test_count} 个Alpha进行质量检测")
        alpha_subset = alpha_list[start_index:max_test_count]
    else:
        max_test_count = len(alpha_list)
        print(f"\n🚀 生产模式：处理全部 {max_test_count} 个Alpha进行质量检测")
        alpha_subset = alpha_list[start_index:]
    
    if not alpha_subset:
        print("✅ 所有Alpha已处理完成")
        if checkpoint_manager:
            checkpoint_manager.clear_checkpoint()
        return 0, 0, []
    
    print(f"\n⚙️ 模拟执行配置：")
    print(f"   - 处理策略数量: {len(alpha_subset)} 个Alpha策略")
    if combination_name:
        print(f"   - 组合名称: {combination_name}")
    print(f"   - 最大重试次数: {alpha_fail_attempt_tolerance}次")
    print(f"   - 自动重连机制: 启用")
    print(f"   - 错误处理: 全面覆盖")
    print(f"   - 日志记录: 启用（双重日志）")
    print(f"   - Day8增强: 断点恢复 + 智能进度监控")
    
    for alpha_index, alpha in enumerate(alpha_subset, start=start_index + 1):
        if graceful_shutdown:
            print("\n🛑 检测到优雅关闭信号，停止处理")
            break
        
        print(f"\n" + "-"*50)
        print(f"📤 正在处理第 {alpha_index} 个Alpha策略 {combo_display}")
        print(f"   Alpha表达式: {alpha['regular']}")
        
        # Day8新增：显示智能进度（用户要求的两种进度条）
        # 1. 长期进度：整体完成情况（16-50小时级别）
        # 2. 短期进度：单个Alpha测试进度（60-180秒级别）
        if progress_tracker:
            progress_tracker.display_progress(alpha_index - start_index)
        
        # 尝试提交Alpha（简化版）
        keep_trying = True
        failure_count = 0
        submission_successful = False
        alpha_id = None
        
        submission_start_time = time.time()
        
        while keep_trying and failure_count < alpha_fail_attempt_tolerance:
            try:
                # 会话健康检查
                sess = maintain_session_health(sess, f"Alpha {alpha_index} 提交")
                
                # 提交Alpha
                api_start_time = time.time()
                sim_resp = sess.post('https://api.worldquantbrain.com/simulations', json=alpha, timeout=30)
                api_response_time = time.time() - api_start_time
                
                # Day8新增：记录API响应时间
                if progress_tracker:
                    progress_tracker.record_api_response(api_response_time)
                
                if sim_resp.status_code == 201:
                    sim_progress_url = sim_resp.headers.get('Location', 'URL not found')
                    print(f"   ✅ Alpha {alpha_index} 提交成功！Location: {sim_progress_url}")
                    
                    # 等待模拟完成并获取Alpha ID（完全继承Day7的完整机制）
                    try:
                        start_time = datetime.now()
                        print(f"   ⏳ 等待模拟完成...")
                        
                        while True:
                            sim_progress_resp = sess.get(sim_progress_url)
                            retry_after_sec = float(sim_progress_resp.headers.get("Retry-After", 0))
                            
                            if retry_after_sec == 0:  # simulation done!
                                # 检查响应状态码（完全继承Day7）
                                if sim_progress_resp.status_code == 200:
                                    response_data = sim_progress_resp.json()
                                    print(f"   📋 模拟完成响应: {response_data}")
                                    
                                    # 尝试从不同字段获取Alpha ID（完全继承Day7）
                                    alpha_id = None
                                    if 'alpha' in response_data:
                                        alpha_id = response_data['alpha']
                                    elif 'alphaId' in response_data:
                                        alpha_id = response_data['alphaId']
                                    elif 'id' in response_data:
                                        alpha_id = response_data['id']
                                    else:
                                        # 从URL中提取ID作为备用方案（完全继承Day7）
                                        alpha_id = sim_progress_url.split('/')[-1] if '/' in sim_progress_url else 'Unknown'
                                        print(f"   ⚠️ 无法从响应中找到Alpha ID，使用URL ID: {alpha_id}")
                                    
                                    print(f"   ✅ 获得 Alpha ID: {alpha_id}")
                                    break
                                else:
                                    print(f"   ❌ 模拟完成但状态码异常: {sim_progress_resp.status_code}")
                                    alpha_id = sim_progress_url.split('/')[-1] if '/' in sim_progress_url else 'Unknown'
                                    break
                            
                            # 进度显示和超时检查（完全继承Day7）
                            elapsed = (datetime.now() - start_time).total_seconds()
                            progress = min(95, (elapsed / 30) * 100)
                            print(f"   ⏳ 等待模拟结果... ({elapsed:.1f} 秒 | 进度约 {progress:.0f}%)")
                            sleep(retry_after_sec)
                            
                            if elapsed > 120:  # 最多等待2分钟（与Day7一致）
                                print(f"   ⚠️ 模拟超时，但Alpha已提交")
                                alpha_id = sim_progress_url.split('/')[-1] if '/' in sim_progress_url else 'Unknown'
                                break
                        
                        successful_submissions += 1
                        submission_successful = True
                        keep_trying = False
                        
                        # Day8新增：记录提交结果
                        if progress_tracker:
                            processing_time = time.time() - submission_start_time
                            progress_tracker.record_submission(processing_time, True)
                        
                    except Exception as e:
                        print(f"   ⚠️ 获取Alpha ID时出错: {str(e)}，但Alpha已成功提交")
                        alpha_id = sim_progress_url.split('/')[-1] if '/' in sim_progress_url else 'Unknown'
                        successful_submissions += 1
                        submission_successful = True
                        keep_trying = False
                        
                        if progress_tracker:
                            processing_time = time.time() - submission_start_time
                            progress_tracker.record_submission(processing_time, True)
                
                else:
                    # 失败处理（完全继承Day7的详细错误处理机制）
                    error_message = f"❌ Alpha {alpha_index} 提交失败，状态码: {sim_resp.status_code}"
                    print(error_message)
                    print(f"   响应内容: {sim_resp.text[:200]}...")
                    logging.error(f"{error_message}, 响应: {sim_resp.text}")
                    
                    # Day7增强：更智能的认证问题处理
                    if sim_resp.status_code in [401, 403]:
                        print("🔐 检测到认证问题，执行重新登录...")
                        try:
                            sess = sign_in()
                            print("✅ 重新认证成功，继续处理")
                            # 重置失败计数，给新会话一个机会
                            if failure_count > 3:
                                failure_count = max(1, failure_count - 2)  # 适度减少失败计数
                                print(f"   ℹ️ 重新认证后调整失败计数为 {failure_count}")
                        except Exception as auth_error:
                            print(f"   ❌ 重新认证失败: {str(auth_error)}")
                            print(f"   ⚠️ 将使用原会话继续尝试")
                    elif sim_resp.status_code >= 500:
                        print("🔧 服务器错误，这通常是临时性问题")
                    elif sim_resp.status_code == 429:
                        print("🚫 请求过于频繁，稍后重试")
                        # 对于限流错误，增加额外等待时间（继承Day7的策略）
                        additional_wait = min(60, 10 * (failure_count + 1))  # 增加等待时间到60秒
                        print(f"   额外等待 {additional_wait} 秒后重试")
                        sleep(additional_wait)
                    
                    failure_count += 1
                    
                    if progress_tracker:
                        processing_time = time.time() - submission_start_time
                        progress_tracker.record_submission(processing_time, False)
                    
            except Exception as e:
                # 通用异常处理（完全继承Day7的异常处理机制）
                general_error = f"🚨 Alpha {alpha_index} 发生未预期异常: {type(e).__name__}: {str(e)}"
                logging.error(general_error)
                print(general_error)
                
                # 检查是否为网络或连接相关错误
                if any(keyword in str(e).lower() for keyword in ['connection', 'timeout', 'network', 'ssl']):
                    print("   🔄 检测到网络问题，尝试重新建立会话...")
                    try:
                        sess = sign_in()
                        print("   ✅ 会话重新建立成功")
                        # 适度减少失败计数，给新会话机会
                        if failure_count > 2:
                            failure_count = max(1, failure_count - 1)
                            print(f"   ℹ️ 网络重连后调整失败计数为 {failure_count}")
                    except Exception as reconnect_error:
                        print(f"   ❌ 重新建立会话失败: {str(reconnect_error)}")
                else:
                    print("   尝试继续使用当前会话...")
                
                failure_count += 1
                
                if progress_tracker:
                    processing_time = time.time() - submission_start_time
                    progress_tracker.record_submission(processing_time, False)
            
            # 重试逻辑和失败处理（完全继承Day7的重试机制）
            if failure_count >= alpha_fail_attempt_tolerance:
                final_error = f"💔 Alpha {alpha_index} 达到最大重试次数 ({alpha_fail_attempt_tolerance})，放弃处理"
                logging.error(final_error)
                print(final_error)
                print("   🔄 最后尝试：重新登录以处理下一个Alpha...")
                try:
                    sess = sign_in()
                    print("   ✅ 为下一个Alpha准备的新会话已就绪")
                except Exception as final_auth_error:
                    print(f"   ❌ 最后的重新认证也失败: {str(final_auth_error)}")
                    print(f"   ⚠️ 将使用当前会话继续处理后续任务")
                
                failure_count = 0  # 重置计数器
                failed_submissions += 1
                keep_trying = False
                break
            
            if keep_trying and not submission_successful:
                wait_time = min(2 ** (failure_count - 1), 15)  # 最大等待15秒
                print(f"   等待 {wait_time} 秒后进行第 {failure_count + 1} 次重试...")
                sleep(wait_time)
        
        # Day8新增：定期保存断点
        if checkpoint_manager and checkpoint_manager.should_save_checkpoint(successful_submissions + failed_submissions):
            checkpoint_manager.save_checkpoint(f"处理到第{alpha_index}个Alpha")
        
        # 单个Alpha处理完成（完全继承Day7的业务流程）
        completion_message = f"📊 Alpha {alpha_index} 处理完成"
        if submission_successful:
            completion_message += " - ✅ 成功"
            print(completion_message)
            logging.info(completion_message)
            
            # 立即进行质量检测（完全继承Day7的整个流程）
            print(f"\n🔍 开始对Alpha {alpha_id} 进行7个标准质量检测...")
            
            try:
                # Day7的等待时间（保持一致）
                sleep(5)  # 等待一下让指标计算完成
                
                alpha_url = f"https://api.worldquantbrain.com/alphas/{alpha_id}"
                alpha_detail = sess.get(alpha_url)
                
                # Day7新增：检查获取Alpha详情时的认证问题（完全继承）
                if alpha_detail.status_code in [401, 403]:
                    print(f"   🔐 检测到认证问题，重新登录后重试...")
                    try:
                        sess = sign_in()
                        alpha_detail = sess.get(alpha_url)  # 重新尝试
                        print(f"   ✅ 重新认证后重试成功")
                    except Exception as detail_auth_error:
                        print(f"   ❌ 重新认证失败: {str(detail_auth_error)}")
                
                if alpha_detail.status_code == 200:
                    alpha_data = alpha_detail.json()
                    
                    if 'is' not in alpha_data:
                        print(f"❌ Alpha {alpha_id} 无法获取指标数据，可能还在计算中")
                        print(f"   建议稍后手动检查Alpha ID: {alpha_id}")
                    else:
                        print(f"✅ 获取到Alpha {alpha_id} 的性能指标")
                        
                        # 进行7个标准的质量检测（完全继承Day7）
                        is_qualified, quality_result = check_alpha_quality(alpha_data)
                        
                        # 显示测试结果（完全继承Day7）
                        display_alpha_result(alpha_id, alpha['regular'], quality_result, combination_name)
                        
                        # 记录到详细日志（完全继承Day7）
                        detailed_logger = logging.getLogger('alpha_details')
                        log_alpha_result(alpha_id, alpha['regular'], quality_result, detailed_logger, combination_name)
                        
                        # 如果合格，保存到文件（完全继承Day7）
                        if is_qualified:
                            save_qualified_alpha(alpha_id, alpha['regular'], quality_result, alpha_index, combination_name)
                            print(f"🎉 Alpha {alpha_id} 通过质量检测并已保存！")
                            qualified_alphas.append({
                                'alpha_id': alpha_id,
                                'expression': alpha['regular'],
                                'index': alpha_index,
                                'combination_name': combination_name,
                                'qualified': True
                            })
                        else:
                            print(f"❌ Alpha {alpha_id} 未通过质量检测")
                            qualified_alphas.append({
                                'alpha_id': alpha_id,
                                'expression': alpha['regular'],
                                'index': alpha_index,
                                'combination_name': combination_name,
                                'qualified': False
                            })
                        
                        log_status = f"Alpha {alpha_id}: {'合格' if is_qualified else '不合格'} - {quality_result['passed_checks']}/{quality_result['total_checks']} 通过"
                        if combination_name:
                            log_status += f" (组合: {combination_name})"
                        logging.info(log_status)
                        
                else:
                    print(f"❌ 无法获取Alpha {alpha_id} 的详细结果，状态码: {alpha_detail.status_code}")
                    
            except Exception as e:
                error_msg = f"❌ 检测Alpha {alpha_id} 时发生错误: {str(e)}"
                if combination_name:
                    error_msg += f" (组合: {combination_name})"
                print(error_msg)
                logging.error(error_msg)
        else:
            completion_message += " - ❌ 失败"
            print(completion_message)
            logging.info(completion_message)
        
        # 显示当前进度
        total_processed = successful_submissions + failed_submissions
        print(f"\n📊 当前进度: {total_processed}/{len(alpha_subset)}")
        print(f"   成功: {successful_submissions}, 失败: {failed_submissions}")
        
        # Day8增强：添加处理间隔以避免频率限制
        if alpha_index < len(alpha_subset):  # 如果不是最后一个Alpha
            interval_wait = 5  # 每个Alpha之间等待5秒
            print(f"   ⏱️ 处理间隔等待 {interval_wait} 秒...")
            sleep(interval_wait)
    
    # Day8新增：完成处理
    if progress_tracker:
        progress_tracker.finish_batch()
    
    # 清除断点
    if checkpoint_manager:
        checkpoint_manager.clear_checkpoint()
    
    print(f"\n" + "="*60)
    print(f"🏁 Day8增强版Alpha策略批量提交完成 {combo_display}")
    print(f"="*60)
    print(f"📈 提交统计报告：")
    print(f"   总处理数量: {successful_submissions + failed_submissions} 个Alpha策略")
    print(f"   成功提交: {successful_submissions} 个")
    print(f"   提交失败: {failed_submissions} 个")
    if successful_submissions + failed_submissions > 0:
        success_rate = successful_submissions / (successful_submissions + failed_submissions) * 100
        print(f"   整体成功率: {success_rate:.1f}%")
    
    return successful_submissions, failed_submissions, qualified_alphas

# ==================== Day8主程序（在Day7基础上增加断点恢复和进度监控） ====================

def show_main_menu_day8():
    """显示Day8主菜单（在Day7基础上增加断点恢复功能）"""
    print("\n" + "="*60)
    print("🚀 WorldQuant Brain Alpha策略智能生成系统 (Day8)")
    print("🆕 完整继承Day7所有功能 + 断点恢复 + 智能进度监控")
    print("="*60)
    print("\n📋 请选择运行模式:")
    print("1. 自动模式 - 生成、测试并提交Alpha策略")
    print("2. 查看模式 - 查看已保存的合格Alpha策略")
    print("3. 恢复模式 - 从断点继续处理")
    print("4. 退出系统")

def main():
    """Day8主程序入口（在Day7基础上增加断点恢复和进度监控）"""
    global current_session, checkpoint_manager, progress_tracker
    
    try:
        # Day8初始化：设置断点恢复系统
        setup_checkpoint_system()
        checkpoint_manager = CheckpointManager()
        progress_tracker = ProgressTracker()
        
        # 完全继承Day7的初始化逻辑，增加组合管理器
        try:
            dataset_manager = DatasetManager()
            combination_manager = CombinationManager()
            detailed_logger = setup_logging()
        except Exception as e:
            print(f"❌ 无法加载配置文件: {e}")
            print("请确保存在datasets_config.json, operators_config.json, combinations_config.json")
            return
        
        print("🔐 正在进行API认证...")
        sess = sign_in()
        current_session = sess
        
        while True:
            show_main_menu_day8()
            
            try:
                choice = input("\n请选择模式 (1-4): ").strip()
                
                if choice == '4':
                    print("👋 感谢使用Day8系统，再见！")
                    break
                    
                elif choice == '2':
                    # 查看合格Alpha模式（完全继承Day7）
                    view_qualified_alphas()
                    input("\n按回车键继续...")
                    continue
                    
                elif choice == '3':
                    # Day8新增：恢复模式
                    checkpoint_data = checkpoint_manager.load_checkpoint()
                    if checkpoint_data:
                        print(f"\n💾 找到断点: {checkpoint_data['timestamp']}")
                        print(f"   原因: {checkpoint_data['reason']}")
                        progress_data = checkpoint_data.get('progress_data', {})
                        print(f"   进度: 已完成 {progress_data.get('completed', 0)}, 失败 {progress_data.get('failed', 0)}")
                        print("\n可使用自动模式选择继续处理")
                    else:
                        print("\n📝 暂无可恢复的断点")
                    input("\n按回车键继续...")
                    continue
                    
                elif choice == '1':
                    # Day8增强的自动模式（完全继承Day7流程）
                    print("\n🎯 Day8自动模式启动 - 支持断点恢复")
                    
                    # 1. 选择数据集（完全继承Day7）
                    selected_dataset = choose_by_category(dataset_manager)
                    dataset_id, search_scope, config = selected_dataset
                    
                    if not dataset_id:
                        print("❌ 无效的数据集选择")
                        continue
                    
                    print(f"\n✅ 已选择数据集: {config['name']}")
                    print(f"   配置: {search_scope}")
                    
                    # 2. 选择运算符组合模式（完全继承Day7）
                    selected_combinations = choose_combination_mode(combination_manager)
                    
                    if not selected_combinations:
                        # 传统模式（Day7原有功能）
                        print("\n🔧 使用传统运算符选择模式:")
                        print("1. 基础模式 - 3种分组运算符 × 3种时间序列运算符")
                        print("2. 高级模式 - 6种分组运算符 × 6种时间序列运算符")
                        
                        complexity_choice = input("请选择复杂度 (1-2): ").strip()
                        operator_set = 'advanced' if complexity_choice == '2' else 'basic'
                        
                        print(f"\n🎯 开始Alpha策略数据准备阶段")
                        print(f"="*60)
                        
                        # 获取数据字段（Day7新增会话检查）
                        sess = maintain_session_health(sess, "传统模式数据获取")
                        datafields_df = get_datafields(sess, search_scope, dataset_id)
                        
                        if datafields_df.empty:
                            print("❌ 未获取到数据字段，请检查网络连接")
                            continue
                        
                        # 过滤MATRIX类型字段
                        matrix_fields = datafields_df[datafields_df['type'] == "MATRIX"]
                        if matrix_fields.empty:
                            print("❌ 未找到MATRIX类型的数据字段")
                            continue
                        
                        datafields_list = matrix_fields['id'].values
                        print(f"\n✅ 数据字段筛选完成：")
                        print(f"   总MATRIX字段数: {len(datafields_list)} 个")
                        print(f"   示例字段: {list(datafields_list[:5])}")
                        
                        # 使用传统方式生成（复用day7逻辑，但简化）
                        op_lib = OperatorLibrary()
                        ops = op_lib.get_operator_set(operator_set)
                        
                        # 简化的Alpha表达式生成（只生成少量用于测试）
                        alpha_expressions = []
                        for gco in ops['group_ops'][:2]:  # 只取前2个运算符
                            for tco in ops['ts_ops'][:2]:
                                for cf in datafields_list[:3]:  # 只取前3个字段
                                    for d in [10]:  # 只用一个周期
                                        for grp in ['sector']:  # 只用一个分组
                                            alpha_expressions.append(f"{gco}({tco}({cf}, {d}), {grp})")
                        
                        print(f"\n✅ 生成了 {len(alpha_expressions)} 个Alpha表达式（简化版）")
                        
                        # 封装并提交
                        alpha_list = create_alpha_list(alpha_expressions, search_scope)
                        
                        print("\n🚀 Day8增强功能：开始批量提交")
                        successful, failed, qualified = batch_submit_alphas_with_day8_enhancements(
                            sess, alpha_list, "传统模式", test_mode=True
                        )
                        
                        print(f"\n📊 传统模式处理完成：成功 {successful}，失败 {failed}")
                        
                    else:
                        # Day7组合模式（完全继承但增加Day8功能）
                        print(f"\n🎯 开始Alpha策略数据准备阶段")
                        print(f"="*60)
                        
                        # 获取数据字段（Day7新增会话检查）
                        sess = maintain_session_health(sess, "组合模式数据获取")
                        datafields_df = get_datafields(sess, search_scope, dataset_id)
                        
                        if datafields_df.empty:
                            print("❌ 未获取到数据字段，请检查网络连接")
                            continue
                        
                        # 过滤MATRIX类型字段
                        matrix_fields = datafields_df[datafields_df['type'] == "MATRIX"]
                        if matrix_fields.empty:
                            print("❌ 未找到MATRIX类型的数据字段")
                            continue
                        
                        datafields_list = matrix_fields['id'].values
                        print(f"\n✅ 数据字段筛选完成：")
                        print(f"   总MATRIX字段数: {len(datafields_list)} 个")
                        print(f"   示例字段: {list(datafields_list[:5])}")
                        
                        # 询问用户处理模式
                        print("\n⚙️ 请选择处理模式:")
                        print("1. 测试模式 - 每个组合只处理前5个Alpha（推荐）")
                        print("2. 生产模式 - 每个组合处理全部Alpha策略（时间很长）")
                        
                        processing_choice = input("请选择模式 (1-2): ").strip()
                        test_mode = processing_choice != '2'  # 默认测试模式
                        
                        # 根据选择的组合进行批量测试
                        all_successful = 0
                        all_failed = 0
                        all_qualified = []
                        
                        for i, combination_config in enumerate(selected_combinations, 1):
                            combo_name = combination_config.get('name', f'组合{i}')
                            
                            print(f"\n" + "="*60)
                            print(f"🔧 Day8测试组合 {i}/{len(selected_combinations)}: {combo_name}")
                            print(f"="*60)
                            
                            # 使用组合配置生成Alpha表达式（简化版）
                            alpha_expressions = generate_alpha_expressions_with_combination(
                                datafields_list[:3], combination_config  # 只使用前3个字段
                            )
                            
                            # 封装Alpha策略
                            alpha_list = create_alpha_list(alpha_expressions, search_scope)
                            
                            # Day8增强批量提交
                            successful, failed, qualified = batch_submit_alphas_with_day8_enhancements(
                                sess, alpha_list, combo_name, test_mode
                            )
                            
                            # 统计累计结果
                            all_successful += successful
                            all_failed += failed
                            all_qualified.extend(qualified)
                            
                            # 组合测试结果总结
                            if qualified:
                                combo_qualified = len([a for a in qualified if a.get('qualified')])
                                print(f"\n📊 组合 '{combo_name}' 测试结果:")
                                print(f"   提交数量: {successful} 个")
                                print(f"   合格数量: {combo_qualified} 个")
                                if successful > 0:
                                    print(f"   合格率: {combo_qualified/successful*100:.1f}%")
                        
                        # 显示最终统计（完全复用day7的逻辑）
                        if all_qualified:
                            truly_qualified = len([a for a in all_qualified if a.get('qualified')])
                            
                            print(f"\n" + "="*60)
                            print(f"🏁 Day8组合模式 Alpha策略质量检测完成")
                            print(f"="*60)
                            print(f"📈 最终统计报告：")
                            print(f"   测试组合数量: {len(selected_combinations)} 个")
                            print(f"   总提交数量: {all_successful} 个Alpha策略")
                            print(f"   质量检测数量: {len(all_qualified)} 个")
                            print(f"   真正合格数量: {truly_qualified} 个")
                            if len(all_qualified) > 0:
                                print(f"   整体合格率: {truly_qualified/len(all_qualified)*100:.1f}%")
                            print(f"\n📁 Day8增强功能:")
                            print(f"   断点恢复: 支持中断后继续处理")
                            print(f"   智能进度: 基于API响应的真实进度显示")
                            print(f"   会话管理: 自动检测和恢复会话")
                        else:
                            print(f"\n❌ 没有成功提交的Alpha策略")
                    
                    input("\n按回车键继续...")
                    
                else:
                    print("❌ 无效选择，请输入1-3之间的数字")
                    
            except KeyboardInterrupt:
                print("\n\n👋 用户中断，退出程序")
                break
            except Exception as e:
                print(f"❌ 程序运行出错: {str(e)}")
                logging.error(f"程序运行出错: {str(e)}")
                input("\n按回车键继续...")
                
    except Exception as e:
        print(f"❌ 程序初始化失败: {str(e)}")
        logging.error(f"程序初始化失败: {str(e)}")

# ==================== Day7兼容性函数（确保完全继承） ====================

def batch_submit_alphas_with_combination(sess, alpha_list, combination_name=None, test_mode=True):
    """
    Day7原始的批量提交函数（完全继承）
    为了保持兼容性，这个函数直接调用Day8的增强版本
    """
    return batch_submit_alphas_with_day8_enhancements(sess, alpha_list, combination_name, test_mode)

def proactive_session_refresh(sess, alpha_index, total_processed):
    """
    主动会话刷新机制（完全继承自Day7）
    
    在以下情况下主动刷新认证：
    1. 每处理50个Alpha后检查一次
    2. 每30分钟检查一次（基于处理速度估算）
    3. 检测到会话可能失效时
    
    参数：
    - sess: 当前会话对象
    - alpha_index: 当前Alpha序号
    - total_processed: 已处理总数
    
    返回：
    - 刷新后的会话对象（可能是新的）
    """
    need_refresh = False
    refresh_reason = ""
    
    # 检查条件1：每50个Alpha检查一次
    if alpha_index % 50 == 0 and alpha_index > 0:
        need_refresh = True
        refresh_reason = f"达到检查间隔（第{alpha_index}个Alpha）"
    
    # 检查条件2：每30分钟强制刷新（估算处理速度：约1个Alpha/分钟）
    elif alpha_index % 30 == 0 and alpha_index > 0:
        need_refresh = True
        refresh_reason = f"定时刷新（约{alpha_index}分钟后）"
    
    if need_refresh:
        print(f"\n🔄 主动会话刷新：{refresh_reason}")
        print(f"   检查当前会话有效性...")
        
        if not check_session_validity(sess):
            print(f"   ⚠️ 检测到会话已失效，执行重新认证")
            try:
                new_sess = sign_in()
                print(f"   ✅ 会话重新认证成功")
                return new_sess
            except Exception as e:
                print(f"   ❌ 重新认证失败: {str(e)}")
                return sess
        else:
            print(f"   ✅ 当前会话仍然有效")
    
    return sess

def show_main_menu_v7():
    """显示Day7主菜单（完全继承）"""
    print("\n" + "="*60)
    print("🚀 WorldQuant Brain Alpha策略智能生成系统 (Day7)")
    print("🔧 新增：运算符组合配置化功能")
    print("="*60)
    print("\n📋 请选择运行模式:")
    print("1. 自动模式 - 生成、测试并提交Alpha策略")
    print("2. 提交模式 - 查看已保存的合格Alpha策略")
    print("3. 退出系统")

if __name__ == "__main__":
    main()