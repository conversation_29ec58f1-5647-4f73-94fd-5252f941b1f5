{"combination_sets": {"trend_following": {"name": "趋势跟踪组合", "description": "适用于捕捉价格趋势的经典组合", "group_ops": ["group_rank", "group_neutralize"], "ts_ops": ["ts_mean", "ts_sum"], "periods": [10, 20], "groups": ["subindustry"]}, "mean_reversion": {"name": "均值回归组合", "description": "适用于短期反转策略的组合", "group_ops": ["group_rank", "group_zscore"], "ts_ops": ["ts_delta", "ts_decay_linear"], "periods": [3, 5, 7], "groups": ["subindustry", "sector"]}, "momentum": {"name": "动量策略组合", "description": "适用于动量效应的组合", "group_ops": ["group_rank", "normalize"], "ts_ops": ["ts_sum", "ts_mean"], "periods": [15, 30], "groups": ["subindustry"]}, "volatility_based": {"name": "波动性策略组合", "description": "基于波动性的策略组合", "group_ops": ["group_zscore", "normalize"], "ts_ops": ["ts_std_dev", "ts_decay_linear"], "periods": [5, 10, 20], "groups": ["subindustry", "sector"]}, "comprehensive": {"name": "全覆盖组合", "description": "使用所有基础运算符的完整组合", "group_ops": ["group_rank", "group_neutralize", "group_zscore"], "ts_ops": ["ts_mean", "ts_sum", "ts_delta"], "periods": [5, 10, 20], "groups": ["subindustry"]}, "conservative": {"name": "保守策略组合", "description": "低风险、稳定的运算符组合", "group_ops": ["group_rank", "normalize"], "ts_ops": ["ts_mean"], "periods": [20, 30], "groups": ["subindustry"]}, "aggressive": {"name": "激进策略组合", "description": "高频交易、快速响应的组合", "group_ops": ["group_zscore", "group_rank"], "ts_ops": ["ts_delta", "ts_sum"], "periods": [3, 5], "groups": ["subindustry", "sector"]}}, "custom_combinations": {"user_defined_1": {"name": "自定义组合1", "description": "用户可以修改此组合的配置", "group_ops": ["group_rank"], "ts_ops": ["ts_mean"], "periods": [10], "groups": ["subindustry"]}}, "combination_metadata": {"version": "1.0", "last_updated": "2025-09-03", "total_combinations": 8, "description": "运算符组合配置文件，定义了各种经典的Alpha策略运算符组合方案"}}