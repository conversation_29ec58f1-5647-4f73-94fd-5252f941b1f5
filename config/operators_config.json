{"operator_categories": {"arithmetic": {"name": "算术运算符", "description": "基础数学运算", "operators": {"abs": {"params": ["x"], "desc": "Absolute value of x"}, "add": {"params": ["x", "y", "filter"], "desc": "Add all inputs (at least 2 inputs required). If filter = true, filter all input NaN to 0 before adding", "syntax": "add(x, y, filter = false), x + y", "default": "filter = false"}, "densify": {"params": ["x"], "desc": "Converts a grouping field of many buckets into lesser number of only available buckets so as to make working with grouping fields computationally efficient"}, "divide": {"params": ["x", "y"], "desc": "x / y", "syntax": "divide(x, y), x / y"}, "inverse": {"params": ["x"], "desc": "1 / x"}, "log": {"params": ["x"], "desc": "Natural logarithm. For example: Log(high/low) uses natural logarithm of high/low ratio as stock weights."}, "max": {"params": ["x", "y", ".."], "desc": "Maximum value of all inputs. At least 2 inputs are required"}, "min": {"params": ["x", "y", ".."], "desc": "Minimum value of all inputs. At least 2 inputs are required"}, "multiply": {"params": ["x", "y", "...", "filter"], "desc": "Multiply all inputs. At least 2 inputs are required. Filter sets the NaN values to 1", "syntax": "multiply(x ,y, ... , filter=false), x * y", "default": "filter=false"}, "power": {"params": ["x", "y"], "desc": "x ^ y"}, "reverse": {"params": ["x"], "desc": " - x"}, "sign": {"params": ["x"], "desc": "if input = NaN; return NaN"}, "signed_power": {"params": ["x", "y"], "desc": "x raised to the power of y such that final result preserves sign of x"}, "sqrt": {"params": ["x"], "desc": "Square root of x"}, "subtract": {"params": ["x", "y", "filter"], "desc": "x-y. If filter = true, filter all input NaN to 0 before subtracting", "syntax": "subtract(x, y, filter=false), x - y", "default": "filter=false"}}}, "logical": {"name": "逻辑运算符", "description": "逻辑判断和比较", "operators": {"and": {"params": ["input1", "input2"], "desc": "Logical AND operator, returns true if both operands are true and returns false otherwise"}, "if_else": {"params": ["input1", "input2", "input3"], "desc": "If input1 is true then return input2 else return input3."}, "<": {"params": ["input1", "input2"], "desc": "If input1 < input2 return true, else return false", "syntax": "input1 < input2"}, "<=": {"params": ["input1", "input2"], "desc": "Returns true if input1 <= input2, return false otherwise", "syntax": "input1 <= input2"}, "==": {"params": ["input1", "input2"], "desc": "Returns true if both inputs are same and returns false otherwise", "syntax": "input1 == input2"}, ">": {"params": ["input1", "input2"], "desc": "Logic comparison operators to compares two inputs", "syntax": "input1 > input2"}, ">=": {"params": ["input1", "input2"], "desc": "Returns true if input1 >= input2, return false otherwise", "syntax": "input1 >= input2"}, "!=": {"params": ["input1", "input2"], "desc": "Returns true if both inputs are NOT the same and returns false otherwise", "syntax": "input1!= input2"}, "is_nan": {"params": ["input"], "desc": "If (input == NaN) return 1 else return 0"}, "not": {"params": ["x"], "desc": "Returns the logical negation of x. If x is true (1), it returns false (0), and if input is false (0), it returns true (1)."}, "or": {"params": ["input1", "input2"], "desc": "Logical OR operator returns true if either or both inputs are true and returns false otherwise"}}}, "time_series": {"name": "时间序列运算符", "description": "时间维度数据处理", "operators": {"ts_rank": {"params": ["x", "d", "constant"], "desc": "Rank the values of x for each instrument over the past d days, then return the rank of the current value + constant. If not specified, by default, constant = 0.", "default": "constant = 0"}, "ts_zscore": {"params": ["x", "d"], "desc": "Z-score is a numerical measurement that describes a value's relationship to the mean of a group of values. Z-score is measured in terms of standard deviations from the mean: (x - tsmean(x,d)) / tsstddev(x,d). This operator may help reduce outliers and drawdown."}, "ts_mean": {"params": ["x", "d"], "desc": "Returns average value of x for the past d days."}, "ts_sum": {"params": ["x", "d"], "desc": "Sum values of x for the past d days."}, "ts_std_dev": {"params": ["x", "d"], "desc": "Returns standard deviation of x for the past d days"}, "ts_delta": {"params": ["x", "d"], "desc": "Returns x - ts_delay(x, d)"}, "ts_delay": {"params": ["x", "d"], "desc": "Returns x value d days ago"}, "ts_corr": {"params": ["x", "y", "d"], "desc": "Returns correlation of x and y for the past d days"}, "ts_covariance": {"params": ["y", "x", "d"], "desc": "Returns covariance of y and x for the past d days"}, "ts_arg_max": {"params": ["x", "d"], "desc": "Returns the relative index of the max value in the time series for the past d days. If the current day has the max value for the past d days, it returns 0. If previous day has the max value for the past d days, it returns 1"}, "ts_arg_min": {"params": ["x", "d"], "desc": "Returns the relative index of the min value in the time series for the past d days; If the current day has the min value for the past d days, it returns 0; If previous day has the min value for the past d days, it returns 1."}, "ts_decay_linear": {"params": ["x", "d", "dense"], "desc": "Returns the linear decay on x for the past d days. Dense parameter=false means operator works in sparse mode and we treat NaN as 0. In dense mode we do not.", "default": "dense = false"}, "ts_product": {"params": ["x", "d"], "desc": "Returns product of x for the past d days"}, "ts_quantile": {"params": ["x", "d", "driver"], "desc": "It calculates ts_rank and apply to its value an inverse cumulative density function from driver distribution. Possible values of driver (optional ) are \"gaussian\", \"uniform\", \"cauchy\" distribution where \"gaussian\" is the default.", "default": "driver=\"gaussian\""}, "ts_regression": {"params": ["y", "x", "d", "lag", "rettype"], "desc": "Returns various parameters related to regression function", "default": "lag = 0, rettype = 0"}, "ts_scale": {"params": ["x", "d", "constant"], "desc": "Returns (x - ts_min(x, d)) / (ts_max(x, d) - ts_min(x, d)) + constant. This operator is similar to scale down operator but acts in time series space", "default": "constant = 0"}, "ts_av_diff": {"params": ["x", "d"], "desc": "Returns x - tsmean(x, d), but deals with NaNs carefully. That is NaNs are ignored during mean computation"}, "ts_backfill": {"params": ["x", "lookback", "k", "ignore"], "desc": "Backfill is the process of replacing the NAN or 0 values by a meaningful value (i.e., a first non-NaN value)", "syntax": "ts_backfill(x,lookback = d, k=1, ignore=\"NAN\")", "default": "k=1, ignore=\"NAN\""}, "ts_count_nans": {"params": ["x", "d"], "desc": "Returns the number of NaN values in x for the past d days"}, "hump": {"params": ["x", "hump"], "desc": "Limits amount and magnitude of changes in input (thus reducing turnover)", "default": "hump = 0.01"}, "kth_element": {"params": ["x", "d", "k"], "desc": "Returns K-th value of input by looking through lookback days. This operator can be used to backfill missing data if k=1"}, "last_diff_value": {"params": ["x", "d"], "desc": "Returns last x value not equal to current x value from last d days"}, "days_from_last_change": {"params": ["x"], "desc": "Amount of days since last change of x"}, "ts_step": {"params": [1], "desc": "Returns days' counter", "syntax": "ts_step(1)", "note": "Always takes parameter 1"}}}, "cross_sectional": {"name": "横截面运算符", "description": "股票间相对比较", "operators": {"rank": {"params": ["x", "rate"], "desc": "Ranks the input among all the instruments and returns an equally distributed number between 0.0 and 1.0. For precise sort, use the rate as 0", "default": "rate=2"}, "zscore": {"params": ["x"], "desc": "Z-score is a numerical measurement that describes a value's relationship to the mean of a group of values. Z-score is measured in terms of standard deviations from the mean"}, "normalize": {"params": ["x", "useStd", "limit"], "desc": "Calculates the mean value of all valid alpha values for a certain date, then subtracts that mean from each element", "default": "useStd = false, limit = 0.0"}, "quantile": {"params": ["x", "driver", "sigma"], "desc": "Rank the raw vector, shift the ranked Alpha vector, apply distribution (gaussian, cauchy, uniform). If driver is uniform, it simply subtract each Alpha value with the mean of all Alpha values in the Alpha vector", "default": "driver = gaussian, sigma = 1.0"}, "scale": {"params": ["x", "scale", "longscale", "shortscale"], "desc": "Scales input to booksize. We can also scale the long positions and short positions to separate scales by mentioning additional parameters to the operator", "default": "scale=1, longscale=1, shortscale=1"}, "winsorize": {"params": ["x", "std"], "desc": "Winsorizes x to make sure that all values in x are between the lower and upper limits, which are specified as multiple of std.", "default": "std=4"}}}, "group": {"name": "分组运算符", "description": "分组内相对操作", "operators": {"group_rank": {"params": ["x", "group"], "desc": "Each elements in a group is assigned the corresponding rank in this group"}, "group_zscore": {"params": ["x", "group"], "desc": "Calculates group Z-score - numerical measurement that describes a value's relationship to the mean of a group of values. Z-score is measured in terms of standard deviations from the mean. zscore = (data - mean) / stddev of x for each instrument within its group."}, "group_neutralize": {"params": ["x", "group"], "desc": "Neutralizes Alpha against groups. These groups can be subindustry, industry, sector, country or a constant"}, "group_scale": {"params": ["x", "group"], "desc": "Normalizes the values in a group to be between 0 and 1. (x - groupmin) / (groupmax - groupmin)"}, "group_mean": {"params": ["x", "weight", "group"], "desc": "All elements in group equals to the mean"}, "group_backfill": {"params": ["x", "group", "d", "std"], "desc": "If a certain value for a certain date and instrument is NaN, from the set of same group instruments, calculate winsorized mean of all non-NaN values over last d days", "default": "std = 4.0"}}}, "vector": {"name": "向量运算符", "description": "向量字段操作", "operators": {"vec_avg": {"params": ["x"], "desc": "Taking mean of the vector field x"}, "vec_sum": {"params": ["x"], "desc": "Sum of vector field x"}}}, "transformational": {"name": "变换运算符", "description": "数据变换和条件操作", "operators": {"bucket": {"params": ["rank(x)", "range", "buckets"], "desc": "Convert float values into indexes for user-specified buckets. Bucket is useful for creating group values, which can be passed to GROUP as input", "syntax": "bucket(rank(x), range=\"0, 1, 0.1\" or buckets = \"2,5,6,7,10\")"}, "trade_when": {"params": ["x", "y", "z"], "desc": "Used in order to change Alpha values only under a specified condition and to hold Alpha values in other cases. It also allows to close Alpha positions (assign NaN values) under a specified condition"}}}}, "operator_sets": {"basic": {"group_ops": ["group_rank", "group_zscore", "group_neutralize"], "ts_ops": ["ts_rank", "ts_zscore", "ts_av_diff"], "periods": [60, 200], "groups": ["market", "industry", "subindustry", "sector"]}, "advanced": {"group_ops": ["group_rank", "group_zscore", "group_neutralize", "group_scale", "group_mean", "group_backfill"], "ts_ops": ["ts_rank", "ts_zscore", "ts_av_diff", "ts_mean", "ts_delta", "ts_corr"], "cross_ops": ["rank", "zscore", "normalize"], "periods": [30, 60, 120, 200, 252], "groups": ["market", "industry", "subindustry", "sector", "densify(pv13_h_f1_sector)"]}}}