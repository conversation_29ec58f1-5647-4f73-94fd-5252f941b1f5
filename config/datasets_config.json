{"datasets": {"analyst4": {"name": "Analyst Estimate Data for Equity", "category": "Analyst/Analyst Estimates", "description": "This dataset provides details and aggregations on opinions from analysts on upcoming fiscal quarters and years. It also includes information about company's financial updates before and after earning announcements. It has global coverage.", "regions": ["USA"], "delays": [0, 1], "universes": ["TOP1000", "TOP200", "TOP3000", "TOP500", "TOPSP500"], "field_count": 350, "coverage": "60%"}, "fundamental2": {"name": "Report Footnotes", "category": "Fundamental/Footnotes", "description": "This dataset holds fundamental items included in the supplements of any related financial statements.", "regions": ["USA"], "delays": [0, 1], "universes": ["TOP1000", "TOP200", "TOP3000", "TOP500", "TOPSP500"], "field_count": 318, "coverage": "41%"}, "fundamental6": {"name": "Company Fundamental Data for Equity", "category": "Fundamental/Fundamental Data", "description": "Fundamental database covers most of the world's big companies. The database also includes industry specific data and updates on quarterly basis. The data is summarized for quarterly/semiannual/yearly periods.", "regions": ["USA"], "delays": [0, 1], "universes": ["TOP1000", "TOP200", "TOP3000", "TOP500", "TOPSP500"], "field_count": 886, "coverage": "74%"}, "model16": {"name": "Fundamental Scores", "category": "Model/Valuation Models", "description": "This dataset ranks stocks based on fundamental and behavioral factors in 5 categories: Valuation, Growth, Profitability, Momentum and Quality. Within these categories, there are additional metrics that have been identified as being key drivers of stock performance. For example looking at valuation, we consider metrics such as price to earnings, price to book value and many more. The fundamental scores are distributed between 0 and 1, and the data frequency is monthly.", "regions": ["USA"], "delays": [1], "universes": ["TOP1000", "TOP200", "TOP3000", "TOP500", "TOPSP500"], "field_count": 8, "coverage": "31%"}, "model51": {"name": "Systematic Risk Metrics", "category": "Model/Risk Models", "description": "This is a risk-model data offering several metrics that can be used to quantify & identify risk. It provides technical information like beta, correlation and unsystematic risk all relative to SPY and systematic risk (also known as idiosyncratic risk) - each for different time periods.", "regions": ["USA"], "delays": [1], "universes": ["TOP1000", "TOP200", "TOP3000", "TOP500", "TOPSP500"], "field_count": 16, "coverage": "77%"}, "news12": {"name": "US News Data", "category": "News/News", "description": "This dataset specializes in matching financial news with market data. They have analyzed 10s of millions of news events and know exactly which ones have an outsized impact on stock prices. So every single time a news item is released they know exactly what the average effect will be on a particular stock.", "regions": ["USA"], "delays": [0, 1], "universes": ["TOP1000", "TOP200", "TOP3000", "TOP500", "TOPSP500"], "field_count": 322, "coverage": "80%"}, "news18": {"name": "Ravenpack News Data", "category": "News/News Sentiment", "description": "This dataset provides news sentiment and other financial indicators. It has a number of different indicators derived from news events relating to different categories & specific companies. Each news event is classified to reflect its sentiment, uniqueness, relevance to underlying company and few other parameters.", "regions": ["USA"], "delays": [0, 1], "universes": ["TOP1000", "TOP200", "TOP3000", "TOP500", "TOPSP500"], "field_count": 75, "coverage": "68%"}, "option8": {"name": "Volatility Data", "category": "Option/Option Volatility", "description": "This is an option dataset which provides historical & option implied volatilities for US equity market. Volatility is available for different time periods using various models. It also includes skew steepness indicators & volatilities for puts, calls and means.", "regions": ["USA"], "delays": [0, 1], "universes": ["TOP1000", "TOP200", "TOP3000", "TOP500", "TOPSP500"], "field_count": 64, "coverage": "69%"}, "option9": {"name": "Options Analytics", "category": "Option/Option Analytics", "description": "This dataset provide options metrics indicating sentiment, and pricing behavior of the market for the underlying stocks. Specifically, this dataset provides 3 metrics: put/call ratios, forward prices, and options breakevens at different horizons. All numbers denote options expiration x days in the future.", "regions": ["USA"], "delays": [1], "universes": ["TOP1000", "TOP200", "TOP3000", "TOP500", "TOPSP500"], "field_count": 74, "coverage": "70%"}, "pv1": {"name": "Price Volume Data for Equity", "category": "Price Volume/Price Volume", "description": "A dataset containing price, volume, close, open, high price, low price, number of shares, cap and related information of each stock.", "regions": ["USA"], "delays": [0, 1], "universes": ["TOP1000", "TOP200", "TOP3000", "TOP500", "TOPSP500"], "field_count": 21, "coverage": "100%"}, "pv13": {"name": "Relationship Data for Equity", "category": "Price Volume/Relationship", "description": "The dataset outputs various classifications and grouping of instruments based on such company connections. Most of grouping fields are sparse, you may always use densify function such as group_neutralize(alpha, densify(group)) to avoid error while using group operators.", "regions": ["USA"], "delays": [0, 1], "universes": ["TOP1000", "TOP200", "TOP3000", "TOP500", "TOPSP500"], "field_count": 168, "coverage": "81%"}, "socialmedia12": {"name": "Sentiment Data for Equity", "category": "Social Media/Social Media", "description": "This dataset provides sentiment data with different sentiment and volume. It analyses social media, news and opinions for financial markets to determine market mood & provide related trading signals. It monitors thousands of sources in real time to output reliable trading signals.", "regions": ["USA"], "delays": [0, 1], "universes": ["TOP1000", "TOP200", "TOP3000", "TOP500", "TOPSP500"], "field_count": 12, "coverage": "96%"}, "socialmedia8": {"name": "Social Media Data for Equity", "category": "Social Media/Social Media", "description": "This is a sentiment dataset based on tweets or twitter messages. This dataset looks at data feed from twitter to quantify predictive social sentiment for different stocks. It also calculates the leading indicator of social sentiment for each security along with other key metrics. The methodology applied here smooths out signals for stocks with less activity on Twitter, thereby preventing spikes in scores & sentiments.", "regions": ["USA"], "delays": [0, 1], "universes": ["TOP1000", "TOP200", "TOP3000", "TOP500", "TOPSP500"], "field_count": 2, "coverage": "86%"}, "univ1": {"name": "Universe Dataset", "category": "Price Volume/Price Volume", "description": "No dataset description", "regions": ["USA"], "delays": [0, 1], "universes": ["TOP1000", "TOP200", "TOP3000", "TOP500", "TOPSP500"], "field_count": 5, "coverage": "36%"}}, "default_config": {"instrumentType": "EQUITY", "region": "USA", "delay": "1", "universe_priority": ["TOP3000", "TOP1000", "TOP500", "TOP200", "TOPSP500"]}}